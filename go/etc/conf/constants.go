package conf

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/client"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	uuid "github.com/satori/go.uuid"
	"strings"
	"time"
)

func init() {
	AwsS3Config = aws.Config{
		Region: aws.String("ca-central-1"),
		Retryer: client.DefaultRetryer{
			NumMaxRetries:    5,
			MinRetryDelay:    10 * time.Millisecond,
			MaxRetryDelay:    50 * time.Millisecond,
			MinThrottleDelay: 500 * time.Millisecond,
			MaxThrottleDelay: 1 * time.Second,
		},
	}
	AwsS3Session = session.Must(session.NewSessionWithOptions(session.Options{
		SharedConfigState: session.SharedConfigEnable,
		Config:            AwsS3Config,
	}))
	AwsS3Client = s3.New(AwsS3Session)
}

/*
AWS Constants
*/
var AwsS3Config aws.Config
var AwsS3Client *s3.S3
var AwsS3Session *session.Session

// *Note*
// These values are used as defaults in the event that env/config values are nil
// CM-2071 introduces timeouts & schedule to env
const AwsMaxRetries = 1
const AwsRequestTimeout = 10 * time.Second
const AwsDocumentRequestTimeout = 20 * time.Second
const ImportScheduleDefault = 1 * time.Hour

/*
Database Constants
*/
//const TenancySiteUrlCachekey = "MasterSiteUrl"
const DatabaseConnectionStringFormat = "host=%s port=%s dbname=%s user=%s password=%s sslmode=disable"

// const TenancyConfigCacheKey = "LocalMasterDatabaseConfig"
const TenancyDatabaseConnectionCacheKey = "LocalMasterDatabaseConnection"
const DatabaseMaxIdleConnections = 1
const DatabaseMaxOpenConnections = 5
const DatabaseMaxConnectionLifetime = 5 * time.Minute

/*
Cache Constants
*/
var TenantSessionPrefixCacheKey = "TenantSession:"
var TenantDatabaseConnectionPrefixCacheKey = "TenantDatabaseConnection:"

/*
ID's
*/
var BcryptUUIDTemplate = "5ea63b02-bfd9-46b4-9e1b-9d3b4b820a7c"
var GoogleAudience = "************-vb7n2t80ib020r6v4v2o398lt1kkr6l6.apps.googleusercontent.com"

/*************************************************************************************************************
Public Webserver Constants
*************************************************************************************************************/
/*
Routing Constants
*/
var RouterApiStatus = "/api/v1/status"
var RouterApiContent = "/api/v1/content"
var RouterApiContentEditors = "/api/v1/content/editors"
var RouterApiRestoreContentById = "/api/v1/content/restore/:id"
var RouterApiCloneContentById = "/api/v1/content/clone/:id"
var RouterApiContentById = "/api/v1/content/:id"
var RouterApiContentPreviewById = "/api/v1/content/preview/:id"
var RouterApiMediaV2 = "/api/v1/media-v2"
var RouterApiS3 = "/api/v1/s3"

var RouterApiSite = "/api/v1/site"
var RouterApiSiteByID = "/api/v1/site/:id"
var RouterApiEvent = "/api/v1/event"
var RouterApiNews = "/api/v1/news"
var RouterApiRole = "/api/v1/role"
var RouterApiDocument = "/api/v1/document"
var RouterApiDocumentByID = "/api/v1/document/:id"
var RouterApiDocumentAncestorsByID = "/api/v1/document/:id/ancestors"
var RouterApiNavigation = "/api/v1/navigation"
var RouterApiNavigationByContentID = "/api/v1/navigation/:id"

var RouterApiLegacyUrlsByContentId = "/api/v1/legacy-urls/:id"

var RouterApiBusArea = "/api/v1/busarea"
var RouterApiBusRoute = "/api/v1/busroute"
var RouterApiBusStatus = "/api/v1/busstatus"
var RouterApiBusStatusById = "/api/v1/busstatus/:id"
var RouterApiBusRouteWithStatus = "/api/v1/combined"
var RouterApiBusRouteWithStatusBySite = "/api/v1/sitecombined"
var RouterApiBusRouteBySite = "/api/v1/sitebusroute"
var RouterApiBusAreaBySite = "/api/v1/sitebusarea"
var RouterApiAlerts = "/api/v1/alerts"

var RouterDocuments = "/documents/:id"
var RouterRootDocuments = "/documents"
var RouterFolderByFilename = "/folder/:id"
var RouterContact = "/api/v1/contact"
var RouterTwitter = "/api/v1/twitter"
var RouterInstagram = "/api/v1/instagram"
var RouterAnonymousPassthrough = "/api/v1/anonymous"
var RouterCalendarFeed = "/api/v1/feed"
var RouterAccountCalendarFeed = "/api/v1/feed/:id"

var RouterAuthorize = "/api/v1/authorize"
var RouterGoogleAuthorize = "/api/v1/googleAuth"
var RouterRedirectAdminConsent = "/api/v1/redirect/consent"
var RouterProcessAdminConsent = "/api/v1/onboarding/consent"
var RouterLogin = "/api/v1/login"
var RouterLogout = "/api/v1/logout"

var SessionCookieExpiry = time.Hour * 24 * 28

var DraftBanner = struct {
	HeadMarker    string
	BannerStyling string
	BannerHTML    string
}{
	HeadMarker: "<head>",
	BannerStyling: " \n" +
		"<style> \n" +
		"	.draft-banner { \n" +
		" 		width:100%; \n" +
		" 		height:3rem;\n" +
		" 		background-color:grey;\n" +
		" 		display:flex;\n" +
		" 		justify-content:center;\n" +
		" 		align-items:center;\n " +
		"	}\n " +
		"   .draft-banner p { \n" +
		"		margin:0;\n" +
		"		font-size:1.15rem\n " +
		"   }\n " +
		"</style>\n",
	BannerHTML: " " +
		"<div class='draft-banner' > " +
		"	<p>" +
		"   	You're viewing a draft - this page is not published " +
		"	</p>" +
		"</div> ",
}

var TemplateContent = "{{templateContent}}"
var EventContentHandlebar = "{{#eventRender"
var SearchHelper = "search-term"
var AzureLoginHandleBar = "{{#AzureLogin}}"
var TransportationAlertHandlebar = "TransportationAlert"
var UseTemplateContentHandlebar = "#useTemplateContent"

var DesignSiteHostSignature = ".cmdesign.imagineeverything.com"
var TestSiteHostSignature = ".test.imagineeverything.ca"

var ImporterAccountId = uuid.FromStringOrNil("45f06f48-a93c-414e-b9a0-7582e0abc085")
var SeedDate = time.Date(2025, time.January, 1, 0, 0, 0, 0, time.UTC)

var DocumentMimeTypes = map[string]string{
	"doc":   "application/msword",
	"dot":   "application/msword",
	"docx":  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	"dotx":  "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
	"xls":   "application/vnd.ms-excel",
	"xlt":   "application/vnd.ms-excel",
	"xla":   "application/vnd.ms-excel",
	"xlsx":  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
	"xltx":  "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
	"xlsm":  "application/vnd.ms-excel.sheet.macroEnabled.12",
	"xltm":  "application/vnd.ms-excel.template.macroEnabled.12",
	"xlam":  "application/vnd.ms-excel.addin.macroEnabled.12",
	"xlsb":  "application/vnd.ms-excel.sheet.binary.macroEnabled.12",
	"ppt":   "application/vnd.ms-powerpoint",
	"pot":   "application/vnd.ms-powerpoint",
	"pps":   "application/vnd.ms-powerpoint",
	"ppa":   "application/vnd.ms-powerpoint",
	"pptx":  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
	"potx":  "application/vnd.openxmlformats-officedocument.presentationml.template",
	"ppsx":  "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
	"ppam":  "application/vnd.ms-powerpoint.addin.macroEnabled.12",
	"pptm":  "application/vnd.ms-powerpoint.presentation.macroEnabled.12",
	"potm":  "application/vnd.ms-powerpoint.template.macroEnabled.12",
	"ppsm":  "application/vnd.ms-powerpoint.slideshow.macroEnabled.12",
	"mdb":   "application/vnd.ms-access",
	"pdf":   "application/pdf",
	"kmz":   "application/vnd.google-earth.kmz",
	"kml":   "application/vnd.google-earth.kml+xml",
	"zip":   "application/zip",
	"ttf":   "font/ttf",
	"woff2": "font/woff2",
}

func TryAddDraftBannerStyling(content string) string {
	start := strings.Index(content, DraftBanner.HeadMarker)
	headLength := start + len(DraftBanner.HeadMarker)
	if start == -1 || headLength > len(content) {
		return content
	}
	return content[:headLength] + DraftBanner.BannerStyling + content[headLength:]
}
