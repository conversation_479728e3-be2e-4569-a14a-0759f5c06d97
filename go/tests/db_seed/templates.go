package db_seed

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/structure"
	_ "embed"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func getStructure(accountID uuid.UUID) structure.Structure {
	defaultStructure := structure.Structure{
		Active:        true,
		Name:          "WYSIWIG",
		Template:      `{{{DctMap.mainContent.lexical.html}}}`,
		FormStructure: []byte(`[{"name": "mainContent", "title": "Main Content", "components": [{"key": true, "name": "lexical", "type": "rich-text", "label": "Content", "required": false, "validate": "", "maximumLength": ""}], "description": "Use the styles dropdown to change text from the 'Normal' style to a heading or list to make your content easier to scan, or use the Insert function to include images in your main content area."}]`),
	}
	defaultStructure.Track(now, accountID)

	return defaultStructure
}

//go:embed templates/template.hbs
var RichTextTemplateContent string

func getRichTextTemplate(sites []uuid.UUID, accountID uuid.UUID, structures []uuid.UUID) commonModels.Content {
	newUuid := uuid.NewV4()
	return commonModels.Content{
		ID:           newUuid,
		Type:         commonModels.Template,
		Title:        "Rich Text Template",
		Content:      RichTextTemplateContent,
		Route:        "",
		PageLayout:   "HTML",
		Created:      now,
		Updated:      now,
		PrivacyLevel: 0,
		Owner:        accountID,
		Publisher:    accountID,
		Approved:     true,
		Active:       true,
		Sites:        sites,
		PublishAt:    &now,
		Settings:     []byte(`{"classification": ["page", "template"]}`),
		Structures:   structures,
		Path:         utils.SanitizeLTree(newUuid.String()),
		TagIds:       []uuid.UUID{},
	}
}

func seedTemplate(db *gorm.DB, accountID uuid.UUID, sites []uuid.UUID) {
	logging.RootLogger().Info().Msg("Seeding template")
	defaultStructure := getStructure(accountID)

	if err := db.Save(&defaultStructure).Error; err != nil {
		panic("Failed to seed defaultStructure: " + err.Error())
	}

	defaultTemplate := getRichTextTemplate(sites, accountID, []uuid.UUID{defaultStructure.ID})
	if err := db.Save(&defaultTemplate).Error; err != nil {
		panic("Failed to seed defaultTemplate: " + err.Error())
	}
}
