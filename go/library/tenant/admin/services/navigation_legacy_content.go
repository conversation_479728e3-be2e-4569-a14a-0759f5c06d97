package adminServices

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/converters"
	"contentmanager/pkgs/structure"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"time"
)

/*
Deprecated
This has been returned due to Navigation (External Link) depending on it.
This file should be deleted & controller decommissioned as soon as we're able to re-write Navigation.
*/
type (
	BaseContent struct {
		ID           uuid.UUID                `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
		Type         commonModels.ContentType `json:"type" gorm:"column:type;type:content_type"`
		Owner        uuid.UUID                `json:"owner" gorm:"column:owner;type:uuid" `
		Publisher    uuid.UUID                `json:"publisher" gorm:"column:publisher;type:uuid"`
		Title        string                   `json:"title" gorm:"column:title;type:character varying(256)"`
		Content      string                   `json:"content" gorm:"column:content;type:text"`
		Data         json.RawMessage          `json:"data" gorm:"column:data;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		Structure    json.RawMessage          `json:"structure" gorm:"column:structure;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		Route        string                   `json:"route" gorm:"column:route;type:character varying(256)"`
		Path         string                   `json:"path" gorm:"column:path;type:ltree"`
		PageLayout   commonModels.PageType    `json:"pagelayout" gorm:"column:pagelayout;type:page_style;DEFAULT:'HTML'::page_style;NOT NULL"`
		Created      time.Time                `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
		Updated      time.Time                `json:"updated" gorm:"column:updated;type:timestamp with time zone"`
		Deleted      time.Time                `json:"deleted" gorm:"column:deleted;type:timestamp with time zone"`
		PrivacyLevel int                      `json:"privacyLevel" gorm:"column:privacy_level;type:integer"`
		Approved     bool                     `json:"approved" gorm:"column:approved;type:boolean;DEFAULT:true"`
		Active       bool                     `json:"active" gorm:"column:active;type:boolean;DEFAULT:true"`
		Sites        dbDriver.PgUUIDArray     `json:"sites" gorm:"column:sites;type:uuid[]"`
		MediaID      uuid.NullUUID            `json:"mediaId" gorm:"column:media_id;type:uuid"`
		Settings     json.RawMessage          `json:"settings" gorm:"column:settings;type:jsonb;NOT NULL;DEFAULT '{}'::jsonb"`
		DepartmentId uuid.NullUUID            `json:"departmentId" gorm:"column:department_id;type:uuid; DEFAULT: NULL"`
		StructureID  *uuid.UUID               `json:"structureId" gorm:"column:structure_id;type:uuid; DEFAULT: NULL"`
		Meta         json.RawMessage          `json:"meta" gorm:"column:meta;type:jsonb`
		PublishAt    *time.Time               `json:"publish_at" gorm:"column:publish_at;type:timestamp with time zone"`
		ExpireAt     *time.Time               `json:"expire_at" gorm:"column:expire_at;type:timestamp with time zone"`
	}
	AdminContent struct {
		BaseContent
		Media            commonModels.Media     `json:"media" gorm:"foreignKey:media_id"`
		Tags             []commonModels.Tag     `json:"tags" gorm:"-"`
		Department       tenancyModels.BaseSite `json:"department"`
		ContentStructure structure.Structure    `gorm:"foreignKey:structure_id"`
		Structures       dbDriver.PgUUIDArray   `json:"structures" gorm:"column:structures;type:uuid[]"`
	}
	ContentViewModel struct {
		AdminContent
		NavigationPath string               `json:"navigationPath" gorm:"navigation_path"`
		Children       []ContentViewModel   `json:"children" gorm:"-"`
		Parent         commonModels.Content `json:"parent" gorm:"-"`
	}
)

func CreateContent(r *shared.AppContext, queryModel commonModels.Content) (commonModels.Content, error) {
	queryModel.Active = true
	queryModel.Created = time.Now()
	queryModel.Updated = queryModel.Created

	if queryModel.Type != commonModels.Page && queryModel.Type != commonModels.Event && queryModel.Type != commonModels.News {
		queryModel.PublishAt = converters.AsPointer(r.AppTime().NowUTC())
	}

	return queryModel, r.TenantDatabase().Create(&queryModel).Error
}
func CreateAndFetchContent(r *shared.AppContext, queryModel commonModels.Content) (ContentViewModel, error) {
	c, err := CreateContent(r, queryModel)
	if err != nil {
		return ContentViewModel{}, err
	}

	return ContentViewModel{
		AdminContent: AdminContent{
			BaseContent: BaseContent{
				ID:           c.ID,
				Type:         c.Type,
				Owner:        c.Owner,
				Publisher:    c.Publisher,
				Title:        c.Title,
				Content:      c.Content,
				Data:         c.Data,
				Structure:    c.Structure,
				StructureID:  c.StructureID,
				Route:        c.Route,
				Path:         c.Path,
				PageLayout:   c.PageLayout,
				Created:      c.Created,
				Updated:      c.Updated,
				Deleted:      c.Deleted,
				PrivacyLevel: c.PrivacyLevel,
				Approved:     c.Approved,
				Active:       c.Active,
				Sites:        c.Sites,
				MediaID:      c.MediaID,
				Settings:     c.Settings,
				DepartmentId: c.DepartmentId,
				Meta:         c.Meta,
				PublishAt:    c.PublishAt,
				ExpireAt:     c.ExpireAt,
			},
			Tags:       c.Tags,
			Media:      c.Media,
			Structures: c.Structures,
		},
		NavigationPath: "",
		Children:       nil,
		Parent:         commonModels.Content{},
	}, nil
}
