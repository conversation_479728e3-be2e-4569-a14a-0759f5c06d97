package adminServices

import (
	tenantDataAccess "contentmanager/library/tenant/admin/dataaccess"
	"contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetContentById(dbCon *gorm.DB, siteId uuid.UUID, contentId uuid.UUID, privacyLevel int) (commonModels.Content, error) {
	return tenantDataAccess.GetContentForSiteById(dbCon, siteId, contentId, privacyLevel)
}
