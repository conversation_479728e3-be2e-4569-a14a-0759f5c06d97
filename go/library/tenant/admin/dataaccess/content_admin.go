package adminDataaccess

import (
	"contentmanager/library/tenant/common/models"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetContentForSiteById(dbCon *gorm.DB, siteId uuid.UUID, contentId uuid.UUID, privacyLevel int) (commonModels.Content, error) {
	var content commonModels.Content
	if contentId == uuid.Nil {
		return content, errors.New("invalid content id")
	}

	dbQuery := dbCon
	if err := dbQuery.
		Where(" id = ? ", contentId).
		Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0) ", privacyLevel).
		Preload("Media", " active = ? ", true).
		//Preload("Tags").
		First(&content).
		Error; err != nil {
		return content, err
	}

	if tags, err := commonModels.MapTagsFromIDs(dbQuery, content.TagIds); err == nil {
		content.Tags = tags
	}
	return content, nil
}
