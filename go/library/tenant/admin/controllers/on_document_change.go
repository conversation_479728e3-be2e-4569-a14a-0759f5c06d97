package adminControllers

import (
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/amzn"
	"contentmanager/pkgs/schoolfinder/admin"
	"contentmanager/pkgs/search_v2/index"
)

func onDocumentChange(r *shared.AppContext, document commonModels.Document, binary string) {
	updateBinary := len(binary) > 0
	index.UpdateIndexForDocumentID(r, document.ID, updateBinary)
	amzn.QueuePatherInvalidations(r, document)
	admin.StoreDocumentPolygons(r.TenantDatabaseUnscoped(), document, binary)
}
