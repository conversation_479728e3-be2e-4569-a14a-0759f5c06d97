package commonModels

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

type DocumentType string

const (
	Folder DocumentType = "folder"
	File   DocumentType = "document"
)

type Document struct {
	ID           uuid.UUID            `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	Title        string               `json:"title" gorm:"column:title;character varying(255);"`
	Filename     string               `json:"filename" gorm:"column:filename;character varying(255); NOT NULL"`
	Path         string               `json:"path" gorm:"column:path;ltree"`
	PrivacyLevel int                  `json:"privacyLevel" gorm:"column:privacy_level;int;NOT NULL;DEFAULT:0"`
	Type         DocumentType         `json:"type" gorm:"column:type;document_type;NOT NULL;DEFAULT 'document'"`
	Sites        dbDriver.PgUUIDArray `json:"sites" gorm:"column:sites;type:uuid[]"`
	DepartmentID *uuid.UUID           `json:"department_id" gorm:"column:department_id;type:uuid; DEFAULT: NULL"`
	Tags         dbDriver.PgUUIDArray `json:"tags" gorm:"column:tags;type:uuid[]"`
	Created      time.Time            `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
	Updated      time.Time            `json:"updated" gorm:"column:updated;type:timestamp with time zone"`
	Deleted      time.Time            `json:"deleted" gorm:"column:deleted;type:timestamp with time zone"`
	Active       bool                 `json:"active" gorm:"column:active;type:bool;NOT NULL"`
	ParentID     uuid.UUID            `json:"parentId" gorm:"-"`
}

func (Document) TableName() string {
	return "document"
}

func (Document) SearchQuery() string {
	return "(id || ' ' || filename) ilike ( ? )"
}

func (d Document) GetUrlPaths() []string {
	return []string{
		fmt.Sprintf("/documents/%s", d.ID),
		fmt.Sprintf("/documents/%s", url.QueryEscape(d.Filename)),
		fmt.Sprintf("/documents/%s/%s", d.ID, url.QueryEscape(d.Filename)),
	}
}

func GetDocumentTitleFromFilename(filename string) string {
	// Remove the file extension (e.g., .pdf)
	ext := filepath.Ext(filename)
	name := strings.TrimSuffix(filename, ext)

	// Replace dashes and underscores with spaces
	name = strings.ReplaceAll(name, "-", " ")
	name = strings.ReplaceAll(name, "_", " ")

	// Insert a space between a lowercase letter and an uppercase letter (for CamelCase words)
	re := regexp.MustCompile(`([a-z])([A-Z])`)
	name = re.ReplaceAllString(name, "$1 $2")

	// Remove any extra whitespace that may have been introduced
	name = strings.Join(strings.Fields(name), " ")

	return name
}

var (
	filenameRegex = regexp.MustCompile("^[a-zA-Z0-9-_]{1,220}\\.[a-z]{3,4}$")
	titleRegex    = regexp.MustCompile("^[\\p{L}\\p{N}][\\p{L}\\p{N}\\p{M}\\p{Zs}',.\\-&()\\[\\]| #]{2,150}$")
)

func DocumentFilenameIsValid(filename string) (bool, error) {
	var filenameWithoutExtension = filename[:len(filename)-len(filepath.Ext(filename))]

	if len(filenameWithoutExtension) == 0 {
		return false, errors.New("document filename cannot be empty")
	}

	if strings.Contains(filename, "/") {
		return false, errors.New("document filename cannot contain forward slashes")
	}

	isValid := filenameRegex.MatchString(filename)

	if !isValid {
		return false, errors.New("filename can only contain letters, numbers, dashes and underscores")
	}

	return true, nil
}

func DocumentTitleIsValid(title string) (bool, error) {
	if len(title) == 0 {
		return false, errors.New("document title cannot be empty")
	}

	isValid := titleRegex.MatchString(title)

	if !isValid {
		return false, errors.New("title can only contain letters, numbers, dashes, underscores, brackets and must start with a letter or number")
	}

	return true, nil
}
