package publicServices

import (
	"contentmanager/library/shared"
	"contentmanager/library/templates/hbs_helpers"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/public/dataaccess"
	"contentmanager/library/tenant/public/features/content"
	"contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/tenant/public/viewmodels"
	"contentmanager/library/utils"
	"contentmanager/pkgs/csp"
	"errors"
	"github.com/PuerkitoBio/goquery"
	uuid "github.com/satori/go.uuid"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
	"net/http"
	"time"
)

var (
	ErrPermissionRequired = errors.New("permission required")
)

func GetContentResults(request *shared.AppContext) (string, error) {
	dbCon := request.TenantDatabase()
	siteId := request.CurrentSiteID()
	account := request.PublicAccount()
	httpRequest := request.Request()
	preview := isPreviewRequest(request.Request())

	contentChain, err := publicDataaccess.GetContentData(dbCon, siteId, httpRequest.URL.Path, preview)
	if err != nil {
		return "", err
	}

	mainContent := contentChain[len(contentChain)-1]
	if mainContent.PrivacyLevel&account.PrivacyLevel != mainContent.PrivacyLevel {
		return "", ErrPermissionRequired
	}
	compiled, err := CompileHandlebars(request, CompileHandlebarsParams{ContentChain: contentChain})
	if err != nil {
		return "", err
	}
	data, err := postprocess(mainContent, compiled)
	if err != nil {
		request.Logger().Err(err).Str("function", "postprocess").Msg("error applying HtmlProcessors")
	}
	return data, nil
}

func postprocess(content publicModels.ContentForHandlebars, htmlDocument string) (string, error) {
	if content.Type == commonModels.JS || content.Type == commonModels.CSS {
		return htmlDocument, nil
	}
	filters := []HtmlProcessor{
		csp.InsertPolicy,
	}
	if res := gjson.Get(string(content.Settings), "ContentIndexingConfig.ExcludeFromIndex"); res.Bool() {
		filters = append(filters, insertMetaTagIfExcludeFromIndex)
	}
	return PostProcessHTML(htmlDocument, filters...)
}

const (
	RobotsNoIndexTag = `<meta name="robots" content="noindex, noarchive">`
)

func insertMetaTagIfExcludeFromIndex(doc *goquery.Document) error {
	if doc.Find(`meta[name="robots"]`).Length() == 0 {
		if res := doc.Find("head"); res.Length() > 0 {
			res.AppendHtml(RobotsNoIndexTag)
		}
	}
	return nil
}
func GetAlerts(dbCon *gorm.DB, siteId uuid.UUID, privacyLevel int, alertType string, lastVisited time.Time) ([]publicModels.ContentForHandlebars, error) {
	params := publiccontent.Params{}.
		WithSiteId(siteId).
		WithPrivacyLevel(privacyLevel).
		WithLastVisited(lastVisited).
		WithContentTypes(publiccontent.Alert).
		WithPublishedOnly().
		WithAllPages()

	var migrated bool
	if err := dbCon.
		Table("content").
		Where("active").
		Where("type = 'template'").
		Where("cardinality(structures) > 0").
		Select("1").
		Limit(1).
		Find(&migrated).Error; err != nil {
		return nil, err
	}

	if migrated {
		if alertType == "popup" {
			params.StructureIDs = []uuid.UUID{uuid.FromStringOrNil("bebde198-ec8b-4bca-b344-194eaa590e99"),
				uuid.FromStringOrNil("6f2297c6-aae6-41c3-a3f8-7deac8c7603d"),
				uuid.FromStringOrNil("9c9df652-d69b-46e2-8dfc-83d472624786")}
		} else if alertType == "banner" {
			params.StructureIDs = []uuid.UUID{uuid.FromStringOrNil("2e7d6dc1-fe00-44b7-b6f2-1a36fcfb3ba8"),
				uuid.FromStringOrNil("ae19c002-2c0e-4423-9099-456d0f0ec3e2")}
		}
	} else {
		params.AlertType = alertType
	}
	//content, err := publicDataaccess.GetContentByParams(dbCon, params)
	return publiccontent.GetByParams(dbCon, params)
}

func GetHTMLAlerts(request *shared.AppContext, alertType string, lastVisited time.Time) ([]publicModels.ContentForHandlebars, error) {
	helpers := hbsHelpers.NewHbsHelpers(request)
	tenantDB := request.TenantDatabase()
	siteId := request.CurrentSiteID()
	contentChain, err := GetAlerts(tenantDB, siteId, request.PublicAccount().PrivacyLevel, alertType, lastVisited)
	if err != nil {
		return contentChain, err
	}
	for i, content := range contentChain {
		if c, e := publicDataaccess.GetContentPathAncestor(tenantDB, siteId, content.Path); e == nil {
			baseViewModel := &viewmodels.BaseContentPage{
				Timezone: request.Timezone(),
				Dct:      content.Data}
			template, e := handlebars.Parse(c.Content)
			if e != nil {
				continue
			}
			result, e := template.ExecWithHelpers(baseViewModel, helpers)
			if e != nil {
				continue
			}
			contentChain[i].Content = result
		}
	}
	return contentChain, nil
}

func GetContentByType(dbCon *gorm.DB, siteId uuid.UUID, contentType string, currentEvents bool, privacyLevel int, tagged bool) ([]publicModels.ContentForHandlebars, error) {
	params := publiccontent.Params{}.
		WithSiteId(siteId).
		WithPrivacyLevel(privacyLevel).
		WithAllPages().
		WithTags().
		WithContentTypes(publiccontent.ToContentType([]string{contentType})...)
	if currentEvents {
		params = params.WithConsiderUpcomingDate()
	}
	if tagged {
		params = params.WithOnlyTagged()
	}
	return publiccontent.GetByParams(dbCon, params)
}

func GetNews(dbCon *gorm.DB, siteId uuid.UUID, privacyLevel int) (allNews []publicModels.ContentForHandlebars, sharedNews []publicModels.ContentForHandlebars, siteNews []publicModels.ContentForHandlebars, err error) {
	allNews, err = publicDataaccess.GetAllNewsForSite(dbCon, siteId, privacyLevel)
	if err != nil {
		return
	}
	for _, v := range allNews {
		if len(v.Sites) == 1 {
			siteNews = append(siteNews, v)
		} else {
			sharedNews = append(sharedNews, v)
		}
	}
	return
}

// Temporary
func GetContentByParams(db *gorm.DB, params publiccontent.Params) ([]publicModels.ContentForHandlebars, error) {
	return publiccontent.GetByParams(db, params)
}

func isPreviewRequest(req *http.Request) bool {
	hash := req.Form.Get("hash")
	prefix := utils.StandardPrefix(req.Host)
	return utils.IsBase64Equal(hash, req.URL.Path, prefix)
}
