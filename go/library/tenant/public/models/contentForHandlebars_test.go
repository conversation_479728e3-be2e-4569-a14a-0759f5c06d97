package publicModels

import (
	"contentmanager/etc/conf"
	"contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/content"
	"contentmanager/tests"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"testing"
)

// TestTag is a local struct for testing purposes to avoid circular dependencies
type TestTag struct {
	ID     uuid.UUID
	Name   string
	Active bool
	Types  driver.PgStringArray `gorm:"column:types;type:text[]"`
}

func (TestTag) TableName() string {
	return "tag"
}

var (
	tag1 = TestTag{
		ID:     uuid.NewV4(),
		Name:   "Sports",
		Active: true,
		Types:  []string{"page", "news", "event", "alert", "fragment"},
	}
	tag2 = TestTag{
		ID:     uuid.NewV4(),
		Name:   "Academic",
		Active: true,
		Types:  []string{"page", "news", "event", "alert", "fragment"},
	}
	toSaveTags = []TestTag{tag1, tag2}

	cfh1 = content.Content{
		Type:       "page",
		Owner:      conf.ImporterAccountId,
		Publisher:  conf.ImporterAccountId,
		PageLayout: commonModels.Html,
		Settings:   []byte("{}"),
		Title:      "1",
		Content:    "Ensure that tags are properly mapped via tag_ids",
		Active:     true,
		Tags:       driver.PgUUIDArray{tag2.ID},
	}
	cfh2 = content.Content{
		Type:       "news",
		Owner:      conf.ImporterAccountId,
		Publisher:  conf.ImporterAccountId,
		PageLayout: commonModels.Html,
		Settings:   []byte("{}"),
		Title:      "2",
		Content:    "Ensure that tags are properly mapped via tag_ids",
		Active:     true,
		Tags:       driver.PgUUIDArray{tag1.ID, tag2.ID},
	}
	toSaveCFH = []content.Content{cfh1, cfh2}
)

func Test_MapTagsForContents(t *testing.T) {
	db, dispose := tests.InitTenantDB()
	defer dispose()

	if err := seedTestData(db); err != nil {
		t.Fatal(err)
	}

	var query []ContentForHandlebars
	if err := db.Where("active").Find(&query).Error; err != nil {
		t.Fatal(err)
	}
	results, err := MapTagsForContents(db, query)
	if err != nil {
		t.Error(err)
	}
	if res1, _ := slicexx.FindFirst(results, func(cfh ContentForHandlebars) bool {
		return cfh.Title == "1"
	}); res1.Tags[0].Name != "Academic" {
		t.Errorf("expected '1' to have the Academic tag - found '%v'", res1.Tags)
	}

	res2, _ := slicexx.FindFirst(results, func(cfh ContentForHandlebars) bool {
		return cfh.Title == "2"
	})
	if len(res2.Tags) != 2 {
		t.Errorf("expected '2' to have two tags - found '%v'", len(res2.Tags))
	}
	for _, tag := range res2.Tags {
		if tag.Name != "Academic" && tag.Name != "Sports" {
			t.Errorf("expected '2' to have the Athletics & Sports tags mapped, got '%v'", res2.Tags)
		}
	}
}

func seedTestData(db *gorm.DB) error {
	var ownerPublisher = identity.Account{
		ID:        conf.ImporterAccountId,
		Firstname: "User",
		Lastname:  "Name",
		Email:     "<EMAIL>",
		Active:    true,
	}
	if err := db.Save(&ownerPublisher).Error; err != nil {
		return err
	}
	if err := db.Save(&toSaveTags).Error; err != nil {
		return err
	}
	return db.Save(&toSaveCFH).Error
}
