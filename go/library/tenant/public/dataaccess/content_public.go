package publicDataaccess

import (
	"contentmanager/etc/conf"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/tenant/public/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
	"time"
)

func GetContentData(dbCon *gorm.DB, siteId uuid.UUID, route string, preview bool) ([]publicModels.ContentForHandlebars, error) {
	var hbc publicModels.ContentForHandlebars
	var contentChain = make([]publicModels.ContentForHandlebars, 0)

	previewClause := "1 = 1"
	if !preview {
		previewClause = pgxx.PublishPeriod(time.Now().UTC(), "", false)
	}

	// TODO => CTE
	if err := dbCon.
		Where(previewClause).
		Where(" lower(route) = lower(?) ", route).
		Where(" type != 'external_link' ").
		Where(" active = true ").
		Where(" ? = ANY(sites)", siteId).
		First(&hbc).
		Error; err != nil {
		return nil, err
	}

	if err := dbCon.
		Where(previewClause).
		Where(" path @> ? ", hbc.Path).
		Where(" nlevel(path) > 0 ").
		Where(" active = true ").
		Order("path asc").
		Preload("Media").
		//Preload("Tags").
		Preload("ContentStructure").
		Find(&contentChain).Error; err != nil {
		return nil, err
	}

	if len(contentChain) == 0 {
		return contentChain, gorm.ErrRecordNotFound
	}

	flattened := contentChain[len(contentChain)-1]

	// Render the content from Structure (if StructureID != nil)
	if flattened.ContentStructure != nil {
		flattened.Content = flattened.ContentStructure.Template
	}
	// End Render the content from Structure

	flattenedContent := flattened.Content
	for i := len(contentChain) - 2; i >= 0; i-- {
		flattenedContent = strings.Replace(contentChain[i].Content, conf.TemplateContent, flattenedContent, -1)
	}
	flattened.Content = flattenedContent

	if !flattened.IsPublished(time.Now()) && preview {
		flattened.Content = conf.DraftBanner.BannerHTML + flattened.Content
		flattened.Content = conf.TryAddDraftBannerStyling(flattened.Content)
	}
	if tags, err := publicModels.MapTagsForContent(dbCon, flattened.TagIds); err == nil {
		flattened.Tags = tags
	}

	contentChain[len(contentChain)-1] = flattened
	return contentChain, nil
}

func GetContentPathAncestor(dbCon *gorm.DB, siteId uuid.UUID, path string) (publicModels.ContentForHandlebars, error) {
	var hbc publicModels.ContentForHandlebars
	dbQuery := dbCon
	if err := dbQuery.
		Where("subpath( ?, 0, nlevel(?)-1) = path", path, path).
		Where("active = true").
		Where("? = any(sites)", siteId).
		First(&hbc).
		Error; err != nil {
		return hbc, err
	}

	return hbc, nil
}
func GetAllNewsForSite(dbCon *gorm.DB, siteId uuid.UUID, privacyLevel int) ([]publicModels.ContentForHandlebars, error) {
	var newsForSite = make([]publicModels.ContentForHandlebars, 0)

	dbQuery := dbCon
	if err := dbQuery.
		Where(" ? = ANY(sites)", siteId).
		Where(" active = true ").
		Where(pgxx.PublishPeriod(time.Now().UTC(), "", false)).
		Where(" type = ?::content_type ", "news").
		//Where(" (settings->>'releaseDate')::timestamptz <= now() ").
		//Where(" (settings->>'expirationDate')::timestamptz >= now() OR (settings->>'expirationDate') IS NULL ").
		Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0)", privacyLevel).
		Order(" (settings ->> 'priority')::double precision asc, coalesce(publish_at, updated, created ) desc ").
		Preload("Media", " active = ? ", true).
		//Preload("Tags").
		Find(&newsForSite).Error; err != nil {
		return nil, err
	}
	// TODO => RRULE:
	// 	 Removed preload(tags), but given this method is only used in very small amount of situations
	//	 I don't think its necessary to care about mapping tags.

	if len(newsForSite) == 0 {
		return newsForSite, gorm.ErrRecordNotFound
	}
	return newsForSite, nil
}
func GetFirstRootTemplate(dbCon *gorm.DB, siteId uuid.UUID) (publicModels.ContentForHandlebars, error) {
	var template publicModels.ContentForHandlebars
	dbQuery := dbCon
	if err := dbQuery.
		Where(" nlevel(path) = 1 ").
		Where(" route is null or route = '' or length(route) = 0 ").
		Where(" type = 'template' ").
		Where(" strpos(content, '{{templateContent}}') > 0 ").
		Where(" active = true ").
		Where(" ? = ANY(sites)", siteId).
		First(&template).
		Error; err != nil {
		return template, err
	}
	return template, nil
}
