package index

import (
	"contentmanager/etc/conf"
	commonModels "contentmanager/library/tenant/common/models"
	publicModels "contentmanager/library/tenant/public/models"
	"gorm.io/gorm"
	"strings"
)

func GetContentChain(tenantDB *gorm.DB, hbc publicModels.ContentForHandlebars) ([]publicModels.ContentForHandlebars, error) {

	var contentChain = make([]publicModels.ContentForHandlebars, 0)

	if err := tenantDB.
		Where(" path @> ? ", hbc.Path).
		Where(" nlevel(path) > 0 ").
		Order("path asc").
		Preload("Media").
		//Preload("Tags").
		Preload("ContentStructure").
		Find(&contentChain).Error; err != nil {
		return nil, err
	}

	if len(contentChain) == 0 {
		return contentChain, gorm.ErrRecordNotFound
	}

	flattened := contentChain[len(contentChain)-1]

	// some pages have incorrect path for some reason, so we need to fix it here
	if flattened.ID != hbc.ID {
		contentChain = append(contentChain, hbc)
		flattened = hbc
	}

	// Render the content from Structure (if StructureID != nil)
	if flattened.ContentStructure != nil {
		flattened.Content = flattened.ContentStructure.Template
	}
	// End Render the content from Structure

	for i := len(contentChain) - 2; i >= 0; i-- {
		flattened.Content = strings.Replace(contentChain[i].Content, conf.TemplateContent, flattened.Content, -1)
	}
	if tags, err := commonModels.MapTagsFromIDs(tenantDB, flattened.TagIds); err == nil {
		flattened.Tags = tags
	}

	contentChain[len(contentChain)-1] = flattened
	return contentChain, nil
}
