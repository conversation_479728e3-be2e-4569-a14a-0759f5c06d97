package public

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/tenancy/models"
	publicModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/content"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
)

type By struct {
	IDsOrNames          []string
	IDs                 []uuid.UUID
	IgnoreSite          bool
	IgnorePublishPeriod bool
	ContentTypes        []string
}

func ContentByParams(request *shared.AppContext, content *[]publicModels.ContentForHandlebars, params By) error {
	if len(params.IDsOrNames) == 0 && len(params.IDs) == 0 {
		return nil
	}
	if len(params.IDsOrNames) > 0 && len(params.IDs) > 0 {
		panic("IDsOrNames and IDs are mutually exclusive")
	}

	var ids []uuid.UUID
	var names []string
	if len(params.IDs) > 0 {
		ids = params.IDs
	} else {
		for _, idOrName := range params.IDsOrNames {
			if id, err := uuid.FromString(idOrName); err != nil {
				names = append(names, idOrName)
			} else {
				ids = append(ids, id)
			}
		}
	}

	dbQuery := request.TenantDatabase().Model(content)
	if !params.IgnoreSite {
		currentSiteID := request.CurrentSiteIDNullable()
		if !currentSiteID.Valid {
			if !request.IsAdminSite() {
				return fmt.Errorf("[ContentByParams] Current site is required. ")
			}
		} else {
			dbQuery = dbQuery.Where(" ? = ANY(sites)", currentSiteID.UUID)
		}

	}
	if !params.IgnorePublishPeriod {
		dbQuery = dbQuery.Where(pgxx.PublishPeriod(request.AppTime().NowUTC(), "", false))
	}
	if len(params.ContentTypes) > 0 {
		dbQuery = dbQuery.Where("type IN (?)", params.ContentTypes)
	}
	if err := dbQuery.
		Where("active").
		Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0) ", request.PublicAccount().PrivacyLevel). // TODO: move to pgxx
		Where(" (id IN (?) OR title IN (?)) ", ids, names).
		Preload("ContentStructure").
		Preload("Media").
		//Preload("Tags").
		Find(content).Error; err != nil {
		return err
	}

	if mapped, err := publicModels.MapTagsForContents(request.TenantDatabase(), *content); err == nil {
		content = &mapped
	}

	return nil
}

func GetPathByID(r *shared.AppContext, id uuid.UUID) (string, error) {
	var c content.Content
	if err := r.TenantDatabase().First(&c, "id = ?", id).Error; err != nil {
		return "", err
	}

	if slicexx.Contains(c.Sites, r.CurrentSiteID()) {
		return c.Route, nil
	}

	domain, err := guessDomain(r, c.Sites)
	if err != nil {
		return "", err
	}

	return "https://" + domain + c.Route, nil
}

func guessDomain(r *shared.AppContext, sites []uuid.UUID) (string, error) {
	var domains []models.Domain
	if err := r.TenancyDB().Where("domain.active").Where(pgxx.FieldInArray("domain.site_id", sites)).Find(&domains).Error; err != nil {
		return "", err
	}

	if len(domains) == 0 {
		return "", utils.ErrNotFound
	}

	for _, d := range domains {
		if strings.Contains(d.Domain, "imagineeverything") {
			continue
		}
		return d.Domain, nil
	}

	return domains[0].Domain, nil
}
