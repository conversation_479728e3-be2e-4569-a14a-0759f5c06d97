package admin2

import (
	"contentmanager/library/utils/mapxx"
	"contentmanager/library/utils/slicexx"
	"contentmanager/library/utils/slicexx/jsonxx"
	"contentmanager/library/utils/xlsxx"
	"contentmanager/pkgs/content"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"log"
	"os"
	"path"
	"slices"
	"strings"
	"time"
)

func ExportToXLSX(rows []content.Content) (string, error) {
	dynamicTitles := map[string]interface{}{}

	mm := []map[string]interface{}{}
	for _, row := range rows {
		m := map[string]interface{}{
			"ID":    row.ID,
			"Title": row.Title,
			"Route": row.Route,
			"Sites": strings.Join(slicexx.Select(row.Sites, func(t uuid.UUID) string {
				return t.String()
			}), ","),
			"DepartmentID": row.DepartmentID,
			"PrivacyLevel": row.PrivacyLevel,
			"PublishAt":    row.PublishAt,
			"ExpireAt":     row.ExpireAt,
			"Type":         row.Type,
			"Path":         row.Path,
			"MediaID":      row.MediaID,
			"StructureID":  row.StructureID,
			"Tags": strings.Join(slicexx.Select(row.Tags, func(t uuid.UUID) string {
				return t.String()
			}), ","),
		}
		if err := flattenJSON(row.Data, m, "Data", dynamicTitles); err != nil {
			return "", err
		}
		if err := flattenJSON(row.Settings, m, "Settings", dynamicTitles); err != nil {
			return "", err
		}
		meta, merr := row.Meta.MarshalJSON()
		if merr != nil {
			return "", merr
		}
		if err := flattenJSON(meta, m, "Meta", dynamicTitles); err != nil {
			return "", err
		}
		mm = append(mm, m)
	}

	addTitles := mapxx.Keys(dynamicTitles)
	slices.Sort(addTitles)
	allTitles := slices.Concat([]string{"ID", "Title", "Route", "Sites", "DepartmentID", "PrivacyLevel", "PublishAt", "ExpireAt", "Type", "Path", "MediaID", "StructureID", "Tags"}, addTitles)
	titlesMap := map[string]int{}
	for idx, title := range allTitles {
		titlesMap[title] = idx
	}
	data := make([][]interface{}, len(mm)+1)
	data[0] = make([]interface{}, len(allTitles))
	for idx, title := range allTitles {
		data[0][idx] = title
	}
	for idx, m := range mm {
		data[idx+1] = make([]interface{}, len(allTitles))
		for k, v := range m {
			data[idx+1][titlesMap[k]] = v
		}
	}

	fileName := path.Join(os.TempDir(), fmt.Sprintf("export-%s.xlsx", time.Now().Format("20060102150405")))
	if err := xlsxx.SaveXLSXFile(fileName, data); err != nil {
		return "", err
	}

	log.Printf("Exported to %s", fileName)
	return fileName, nil
}

func flattenJSON(data []byte, rowCells map[string]interface{}, prefix string, titles map[string]interface{}) error {
	if len(data) == 0 {
		titles[prefix] = struct{}{}
		rowCells[prefix] = nil
		return nil
	}
	m, e := jsonxx.FlattenJSON(data)
	if e != nil {
		return e
	}

	for k, v := range m {
		// Skip lexical.json
		if strings.Contains(k, ".lexical.json") {
			continue
		}

		rowCells[prefix+"."+k] = v
		titles[prefix+"."+k] = struct{}{}
	}
	return nil
}
