package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/content"
	"encoding/base64"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
)

var (
	ErrInactiveContent = errors.New("cannot preview inactive content")
)

func GetContentPreviewURL(r *shared.AppContext, contentForPreview content.Content) (string, error) {
	if !contentForPreview.Active {
		return "", ErrInactiveContent
	}
	domain, err := getPreviewDomain(r)
	if err != nil {
		return "", fmt.Errorf("failed to determine preview domain [%s]", err.Error())
	}
	if logging.IsDebug() && !strings.HasSuffix(domain, ".localhost") {
		domain += ".localhost"
	}
	route := domain + contentForPreview.Route
	hash := utils.StandardPrefix(domain) + base64.StdEncoding.EncodeToString([]byte(contentForPreview.Route))
	return fmt.Sprintf("https://%s?hash=%s", route, hash), nil

}

func getPreviewDomain(r *shared.AppContext) (string, error) {
	previewSiteID := uuid.FromStringOrNil(r.Request().Form.Get("previewSiteID"))
	if previewSiteID == uuid.Nil || previewSiteID == r.CurrentSiteID() {
		return r.CurrentSite().PrimaryDomain, nil
	}
	site, err := r.SiteByID(previewSiteID)
	if err != nil {
		return "", err
	}
	return site.PrimaryDomain, nil
}
