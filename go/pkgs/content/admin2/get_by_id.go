package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/content"
	uuid "github.com/satori/go.uuid"
)

type ContentWithTags struct {
	content.Content
	Tags []uuid.UUID
}

func GetByID(r *shared.AppContext, id uuid.UUID) result.Result[ContentWithTags] {
	var ct ContentWithTags
	var c content.Content
	if err := r.TenantDatabase().Find(&c, "id = ?", id).Error; err != nil {
		return result.Error(err, ct)
	}

	return result.Success(ContentWithTags{c, c.Tags})
}
