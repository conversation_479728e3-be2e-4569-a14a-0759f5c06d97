package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/reservation"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
)

func SetStatus(r *shared.AppContext, id uuid.UUID, status bool) result.EmptyResult {
	var dbContent content.Content
	if err := r.TenantDatabase().Where("id = ?", id).First(&dbContent).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	// StartOrExtendSession has empty Params here to avoid starting a session
	// it may be more intuitive to have `dbContent.IsEditingSessionForUserOrAvailable(nil, &r.Account().ID)` since we're not interested in the `res.apply` here.
	// (Option A)
	if err := dbContent.StartOrExtendSession(reservation.ReservableParams{}, r.Account()); err != nil {
		return result.ErrorEmpty(err)
	}
	// (Option B)
	//		if !dbContent.IsEditingSessionForUserOrAvailable(nil, &r.Account().ID) {
	//			return result.ErrorEmpty(reservation.ErrEditingSessionConflict)
	//		}
	// OR

	if !status && dbContent.Type == commonModels.Template {
		subPath := strings.ReplaceAll(dbContent.ID.String(), "-", "")
		var count int64
		if err := r.TenantDatabase().Model(&content.Content{}).Where("active and id != ? and path::text like ?", dbContent.ID, "%"+subPath+"%").Count(&count).Error; err != nil {
			return result.ErrorEmpty(err)
		}
		if count > 0 {
			return result.ErrorEmpty(fmt.Errorf("Cannot delete template because it is used by %v active content. ", count))
		}
	}

	dbContent.Active = status
	if status {
		dbContent.PublishPeriod.PublishAt = nil
	}
	dbContent.Publisher = r.Account().ID
	dbContent.Updated = r.AppTime().NowUTC()
	if err := r.TenantDatabase().Save(&dbContent).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	onContentChanged(r, dbContent)

	return result.SuccessEmpty()
}

func Expire(r *shared.AppContext, id uuid.UUID) result.EmptyResult {
	var dbContent content.Content
	if err := r.TenantDatabase().Where("id = ?", id).First(&dbContent).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if err := dbContent.StartOrExtendSession(reservation.ReservableParams{}, r.Account()); err != nil {
		return result.ErrorEmpty(err)
	}

	if dbContent.Type == commonModels.Template {
		subPath := strings.ReplaceAll(dbContent.ID.String(), "-", "")
		var count int64
		if err := r.TenantDatabase().Model(&content.Content{}).Where("active and id != ? and path::text like ?", dbContent.ID, "%"+subPath+"%").Count(&count).Error; err != nil {
			return result.ErrorEmpty(err)
		}
		if count > 0 {
			return result.ErrorEmpty(fmt.Errorf("Cannot delete template because it is used by %v active content. ", count))
		}
	}

	now := r.AppTime().NowUTC()

	dbContent.ExpireAt = &now
	dbContent.Publisher = r.Account().ID
	dbContent.Updated = r.AppTime().NowUTC()
	if err := r.TenantDatabase().Save(&dbContent).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	onContentChanged(r, dbContent)

	return result.SuccessEmpty()
}
