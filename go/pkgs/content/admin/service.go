package admin

import (
	"contentmanager/infrastructure/database/pgxx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"log"
	"strings"
	"time"
)

func MapTagsForContents(db *gorm.DB, content []ContentForManager) ([]ContentForManager, error) {
	var tags = []commonModels.Tag{}
	if err := db.
		Table(commonModels.Tag{}.TableName()).
		Where(pgxx.ArrayHasAny("types", commonModels.ContentTagTypes)).
		Where("active").
		Find(&tags).Error; err != nil {
		return content, err
	}
	m := slicexx.AsMap(tags, func(t commonModels.Tag) uuid.UUID {
		return t.ID
	})
	for i, c := range content {
		var contentTags = []commonModels.Tag{}
		for _, tagId := range c.TagIds {
			if tag, ok := m[tagId]; ok {
				contentTags = append(contentTags, tag)
			}
		}
		content[i].Tags = contentTags
	}
	return content, nil
}

func GetByParams(db *gorm.DB, params Params) ([]ContentForManager, int64, error) {
	ve := params.Validate()
	if ve != nil {
		return nil, 0, ve
	}

	contentChain := []ContentForManager{}
	var contentLength int64
	dbQuery := db.Model(&contentChain)

	if params.IncludeAdminData {
		dbQuery = dbQuery.Select("content.*, navigation.path nav_path, template.title template_title")
		dbQuery = dbQuery.Joins("left join (select distinct on (navigation.content_id) * from navigation where navigation.site_id = ? and navigation.active = true) navigation on navigation.content_id = content.id", params.SiteId)
		dbQuery = dbQuery.Joins("left join content as template on subpath ( content.path, -2, 1 )::text::uuid = template.id")
	}

	// TODO => NST-128
	// Board Office = B1
	// Department = D1

	//1 Get Content request from D1, no departments
	// SiteId = D1, Departments = [D1]
	// => (department_id = myDepartment AND department_id IN [D1])
	// PASS

	//2 <NOT IMPLEMENTED, SEARCH FOR DEPARTMENT CONTENT BY SITES CASE>
	// I'm on a department (D1), I make a request for B1 content
	//"departments" [B1], SiteId = D1
	// => (department_id in (B1) or D1 = any(sites))
	//  FAIL.. but if we switch how we're using departments, just like site(s)
	// - SiteId = D1
	// - "Departments" = [B1]
	// => (department_id = D1 AND [B1] && content.sites)
	// PASS
	// This is a possible short-term solution for "search department content by sites"

	//3 Get Content request from B1, no departments
	// SiteId = B1
	// => (B1 = any(sites))
	// PASS

	//4 Get Content request from B1, I make a request for D1 content
	// Department[D1], SiteId = B1
	// (department_id in (D1) AND B1 = any(sites))
	// PASS

	// 1
	// TODO => Navigation
	//  Search Pages shouldn't include results where department_id is not null
	if params.IsDepartment {
		dbQuery = dbQuery.Where("content.department_id = ?", params.SiteId)
	} else {
		if params.SiteOnly {
			dbQuery = dbQuery.Where(" ( ? = ANY(content.sites) AND content.department_id IS NULL AND array_length(content.sites, 1) = 1) ", params.SiteId)
		} else {
			dbQuery = dbQuery.Where("? = any(content.sites)", params.SiteId)
		}
		if params.HideDepartmentContent {
			dbQuery = dbQuery.Where("content.department_id IS NULL AND navigation.department_id IS NULL")
		}
	}
	if len(params.Departments) > 0 {
		dbQuery = dbQuery.Where("content.department_id IN (?)", params.Departments)
	}

	// 2 Fails on case: SiteId = Board Office, searching by department.
	// - Departments = [D1], SiteId = B1, Query = (department_id IN (D1) OR B1 = any(sites))
	// - This searches for any of D1 content + all B1 content.
	//dbQuery = dbQuery.Where("(content.department_id IN (?) OR ? = any(content.sites))", params.Departments, params.SiteId)

	// 3 Fails on case: SiteId = Department - cannot use AND for a department
	//if len(params.Departments) > 0 {
	//	dbQuery = dbQuery.Where("(content.department_id IN (?) AND ? = any(content.sites))", params.Departments, params.SiteId)
	//} else {
	//	dbQuery = dbQuery.Where("? = any(content.sites)", params.SiteId)
	//}

	dbQuery = processWhere(dbQuery, params)
	dbQuery = processEditable(dbQuery, params)

	countErr := dbQuery.Count(&contentLength).Error

	if countErr != nil {
		return nil, 0, countErr
	}
	if contentLength == 0 {
		return contentChain, contentLength, nil
	}

	// Order By
	dbQuery = applySortOrder(dbQuery, params)

	// Associations
	if params.IncludeMedia {
		dbQuery = dbQuery.Preload("Media", " active = ? ", true)
	}
	//if params.IncludeTags {
	//	dbQuery = dbQuery.Preload("Tags")
	//}

	err := dbQuery.
		Offset(params.Skip).
		Limit(params.Limit).
		Find(&contentChain).
		Error

	if err != nil {
		return nil, 0, err
	}

	if params.IncludeAdminData {
		preloadParents(db, contentChain)
		preloadEditors(db, contentChain)
	}
	if params.IncludeTags {
		if mapped, err := MapTagsForContents(db, contentChain); err == nil {
			contentChain = mapped
		}
	}

	return contentChain, contentLength, nil
}

func preloadEditors(db *gorm.DB, contentChain []ContentForManager) {
	patentsIds := make(map[uuid.UUID]interface{})
	for _, c := range contentChain {
		if c.Owner != uuid.Nil {
			patentsIds[c.Owner] = struct{}{}
		}
		if c.Publisher != uuid.Nil {
			patentsIds[c.Publisher] = struct{}{}
		}
	}
	ids := make([]uuid.UUID, len(patentsIds))
	i := 0
	for k := range patentsIds {
		ids[i] = k
		i++
	}
	aa := []AccountName{}
	e := db.Table("account").Select("id, firstname, lastname").Where("id in (?)", ids).Scan(&aa).Error
	if e != nil {
		return
	}
	aMap := make(map[uuid.UUID]AccountName, len(aa))
	for _, a := range aa {
		aMap[a.ID] = a
	}
	for i, _ := range contentChain {
		contentChain[i].OwnerName = aMap[contentChain[i].Owner]
		contentChain[i].PublisherName = aMap[contentChain[i].Publisher]
	}
}

// TODO => Preload parents doesn't take into account parents whose navigation is inactive (disconnected page).
func preloadParents(db *gorm.DB, contentChain []ContentForManager) {
	parentIds := make(map[uuid.UUID]interface{})
	for _, c := range contentChain {
		if len(c.NavPath) > 0 {
			for _, s := range strings.Split(c.NavPath, ".") {
				u := uuid.FromStringOrNil(s)
				if u == uuid.Nil {
					continue
				}

				parentIds[u] = struct{}{}
			}
		}
	}
	if len(parentIds) > 0 {
		ids := make([]uuid.UUID, len(parentIds))
		i := 0
		for k := range parentIds {
			ids[i] = k
			i++
		}
		var pp []ContentNavParent
		e := db.
			Table("content").
			Select("content.id, title, route").
			Joins(" JOIN navigation n on n.content_id = content.id and n.active = true ").
			Where("content.id in (?)", ids).
			Scan(&pp).
			Error
		if e == nil {
			parentsMap := make(map[uuid.UUID]ContentNavParent, len(pp))
			for _, p := range pp {
				parentsMap[p.ID] = p
			}
			for i, c := range contentChain {
				for _, s := range strings.Split(c.NavPath, ".") {
					u := uuid.FromStringOrNil(s)
					if u == uuid.Nil {
						continue
					}
					if _, ok := parentsMap[u]; !ok {
						c.NavParents = []ContentNavParent{{ID: uuid.NewV4(), Title: "Disconnected From Navigation"}}
						break
					}
					c.NavParents = append(c.NavParents, parentsMap[u])
				}
				contentChain[i] = c
			}
		}
	}
}

func applySortOrder(dbQuery *gorm.DB, params Params) *gorm.DB {
	if len(params.Sortings) > 0 {
		for _, s := range toSortStrings(params.Sortings) {
			dbQuery = dbQuery.Order(s)
		}
	} else if len(params.ContentType) == 0 {
		dbQuery = dbQuery.Order("content.title asc")
	} else {
		if params.HasAnyTypeOf([]Type{Page}) {
			dbQuery = dbQuery.Order("nav_path asc, coalesce(content.updated, content.created)")
		}
		if params.HasAnyTypeOf([]Type{JS, Template, CSS}) {
			dbQuery = dbQuery.Order("content.title asc")
		}
		if params.HasAnyTypeOf([]Type{Event, Alert}) {
			dbQuery = dbQuery.Order("(content.settings->>'startdate') desc")
		}
		if params.HasAnyTypeOf([]Type{News}) {
			dbQuery = dbQuery.Order("(content.settings->>'priority')::double precision asc, coalesce(content.publish_at, content.updated, content.created) desc")
		}
	}
	return dbQuery
}

func processEditable(dbQuery *gorm.DB, params Params) *gorm.DB {
	if !params.OnlyEditable {
		return dbQuery
	}
	if len(params.ContentType) != 1 {
		return dbQuery
	}

	dbQuery = dbQuery.Where(`
		case
			when array_length(content.sites, 1) = 1 and ARRAY[?]::uuid[] @> content.sites then true
			when array_length(content.sites, 1) > 1 and ARRAY[?]::uuid[] @> content.sites then true
		else false
		end
	`, params.IdsForCommon, params.IdsForDistributed)

	return dbQuery
}

func processWhere(dbQuery *gorm.DB, params Params) *gorm.DB {

	dbQuery = applyStatus(dbQuery, params)

	// dbQuery = dbQuery.Where(" ((content.privacy_level & ?) = content.privacy_level OR content.privacy_level = 0) ", params.PrivacyLevel)

	if params.OnlyTagged {
		dbQuery = dbQuery.Where("array_length(content.tags, 1) > 0")
	}
	if len(params.ContentType) > 0 {
		dbQuery = dbQuery.
			Where(" content.type IN (?)", params.ContentType)
	}
	if params.UsePriority {
		dbQuery = dbQuery.
			Where(" (content.settings->>'priority') IS NOT NULL")
	}
	if !params.LastVisited.IsZero() {
		dbQuery = dbQuery.
			Where(" ? < content.updated ", params.LastVisited)
	}
	if len(params.Search) > 0 {
		subqueries, rest := pgxx.ExtractCommands(params.Search)
		if len(rest) > 0 {
			dbQuery = dbQuery.Where("( content.title || ' ' || content.id || ' ' || coalesce(content.route, ' ')) ilike ( ? )", "%"+params.Search+"%")
		}
		for _, subquery := range subqueries {
			if subquery.Column == "title" {
				dbQuery = dbQuery.Where("content.title ilike ?", "%"+subquery.Keyword+"%")
			} else if subquery.Column == "route" {
				dbQuery = dbQuery.Where("content.route ilike ?", "%"+subquery.Keyword+"%")
			} else if strings.HasPrefix(subquery.Column, "settings.") {
				dbQuery = dbQuery.Where("content.settings->>? ilike ?", strings.TrimPrefix(subquery.Column, "settings."), "%"+subquery.Keyword+"%")
			}
		}
	}
	if params.OnlyWithDct {
		dbQuery = dbQuery.Where(" (content.structure IS NOT NULL or content.structure != '\"\"') ")
	}
	if len(params.Classification) > 0 {
		dbQuery = dbQuery.Where(" (content.settings->>'classification')::jsonb @> ?::jsonb  ", "[\""+params.Classification+"\"]")
	}
	if params.Imported {
		dbQuery = dbQuery.Where("(content.settings->>'imported')::boolean = true")
		if len(params.ImportSource) > 0 {
			//dbQuery = dbQuery.Where(" (settings->>'source')::text = ? ", params.ImportSource)
			dbQuery = dbQuery.Where(" content.settings#>>'{importInfo, source}' = ? ", params.ImportSource)
		}
	}
	if len(params.AlertType) > 0 {
		dbQuery = dbQuery.Where(" (content.settings->>'alertType') = ? ", params.AlertType)
	}
	if len(params.Tags) > 0 {
		dbQuery = dbQuery.Where(pgxx.ArrayHasAny("content.tags", params.Tags))
	}
	if len(params.Editors) > 0 {
		dbQuery = dbQuery.Where("(content.owner in (?) OR content.publisher in (?))", params.Editors, params.Editors)
	}
	if len(params.Filters) > 0 {
		dbQuery = processFilters(dbQuery, params.Filters)
	}
	return dbQuery
}

func applyStatus(dbQuery *gorm.DB, params Params) *gorm.DB {
	if !params.DeletedAfter.IsZero() {
		dbQuery = dbQuery.Where("content.active = ? AND content.deleted >= ? ", false, params.DeletedAfter)
		return dbQuery
	}

	dbQuery = dbQuery.Where("content.active = ?", !params.Inactive)

	if len(params.Status) > 0 {
		dbQuery = dbQuery.Where(pgxx.Status(time.Now(), "content.", params.Status))
	}

	if params.ConsiderStartDate {
		dbQuery = dbQuery.Where(" (content.settings->>'startdate')::timestamptz < now() ")
	}
	if params.ConsiderEndDate {
		dbQuery = dbQuery.Where(" (content.settings->>'enddate')::timestamptz > now() ")
	}
	if params.ConsiderUpcomingDate {
		dbQuery = dbQuery.Where("(content.settings->>'startdate')::timestamptz > now() ")
	}

	return dbQuery
}

func processFilters(dbQuery *gorm.DB, filters []Filter) *gorm.DB {
	for _, f := range filters {
		if len(f.Value) == 0 {
			continue
		}

		if strings.ToLower(f.ColumnField) == "pagelayout" {
			where := "content.pagelayout"
			if f.OperatorValue == "not" {
				where += " <> ?"
			} else {
				where += " = ?"
			}
			if f.Value == "WYSIWYG" ||
				f.Value == "HTML" ||
				f.Value == "DCT" {
				dbQuery = dbQuery.Where(where, f.Value)
			} else {
				continue
			}
		}

		if strings.ToLower(f.ColumnField) == "privacylevel" {
			if len(f.Value) == 0 {
				continue
			}

			where := "content.privacy_level"
			if strings.ToLower(f.Value) == "undefined" {
				if f.OperatorValue == "not" {
					dbQuery = dbQuery.Where(where + " is not null")
				} else {
					dbQuery = dbQuery.Where(where + " is null")
				}
			} else {
				v := -1
				switch strings.ToLower(f.Value) {
				case "public":
					v = 0
				case "staff":
					v = 2
				}

				if v == -1 {
					continue
				}

				if f.OperatorValue == "not" {
					dbQuery = dbQuery.Where(where+" <> ?", v)
				} else {
					dbQuery = dbQuery.Where(where+" = ?", v)
				}
			}
		}

		if strings.ToLower(f.ColumnField) == "template" {
			dbQuery = dbQuery.Where("template.title ilike (?)", "%"+f.Value+"%")
		}

		if strings.ToLower(f.ColumnField) == "updated" {
			where := "coalesce(content.updated, content.created)"
			date, err := time.Parse("2006-01-02", f.Value)

			if err != nil {
				log.Print(err)
				continue
			}
			if f.OperatorValue == "onOrBefore" {
				where += " <= ?"
				date = date.Add(time.Hour * 24)
			} else {
				where += " >= ?"
			}

			dbQuery = dbQuery.Where(where, date)
		}
	}
	return dbQuery
}

func toSortStrings(sort []Sort) []string {
	res := []string{}
	hasUpdated := false
	for _, s := range sort {
		sortString := ""

		switch strings.ToLower(s.Field) {
		case "updated":
			hasUpdated = true
			sortString = "coalesce(content.updated, content.created)"
		case "title":
			sortString = "content.title"
		case "pagelayout":
			sortString = "content.pagelayout"
		case "startdate":
			sortString = "(content.settings ->> 'startdate')::timestamp with time zone"
		case "enddate":
			sortString = "(content.settings ->> 'enddate')::timestamp with time zone"
		case "priority":
			sortString = "(content.settings->>'priority')::double precision"
		case "publish_at", "releasedate":
			sortString = "content.publish_at"
		case "expire_at", "expirationdate":
			sortString = "content.expire_at"
		}

		if len(sortString) == 0 {
			continue
		}

		if strings.ToLower(s.Order) == "desc" {
			sortString += " desc nulls LAST "
		} else {
			sortString += " asc nulls LAST "
		}
		res = append(res, sortString)
	}
	if !hasUpdated {
		res = append(res, "coalesce(content.updated, content.created) desc")
	}
	return res
}
