package add_admin

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/admin2"
	"contentmanager/pkgs/content/resources/admin"
	commonModels "contentmanager/pkgs/reservation"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"os"
	"strings"
)

func AddContentV2(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v2/content", func(router httpService.Router) {

		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery admin2.SearchQuery
		}) {
			utils.WriteResultJSON(w, admin2.Search(r, params.FromQuery))
		})

		router.Post("/import/xlsx", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromMultipartForm admin2.ImportXLSXParams
		}) {
			if params.FromMultipartForm.File == nil {
				utils.WriteError(w, errors.New("File is required"))
				return
			}

			contentType := strings.ToLower(params.FromMultipartForm.File.Header.Get("Content-Type"))
			switch contentType {
			case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
				utils.WriteResultJSON(w, admin2.ImportXLSX(r, params.FromMultipartForm))
			case "text/calendar":
				utils.WriteResultJSON(w, admin2.ImportICS(r, params.FromMultipartForm))
			default:
				utils.WriteError(w, errors.New("Invalid file type. Expected XLSX or ICS"))
			}
		})

		router.Get("/export/xlsx", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery admin2.SearchQuery
		}) {
			result := admin2.Search(r, params.FromQuery)
			if result.IsError() {
				utils.WriteResponseJSON(w, nil, result.Unwrap())
				return
			}

			// Export to XLSX
			filePath, err := admin2.ExportToXLSX(result.Data.Rows)
			defer func(name string) {
				err := os.Remove(name)
				if err != nil {
					r.Logger().Error().Err(err).Msg("Failed to remove file")
				}
			}(filePath)

			if err != nil {
				r.Logger().Error().Err(err).Msg("Failed to export to XLSX")
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			if err := utils.WriteLocalFile(w, utils.WriteLocalFileParams{
				FilePath:    filePath,
				ContentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			}); err != nil {
				r.Logger().Error().Err(err).Msg("Failed to write local file")
				utils.WriteResponseJSON(w, nil, err)
			}
		})

		router.Get("/priority/max", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery admin2.SearchQuery
		}) {
			var maxPriority int

			err := r.TenantDatabase().Table("content").
				Select("COALESCE(MAX((settings->>'priority')::numeric), -1)").
				Where("active").
				Where("type='news'").
				Where("settings ? 'priority'").
				Where("(settings->>'priority')::numeric IS NOT NULL").
				Scan(&maxPriority).
				Error
			utils.WriteResponseJSON(w, maxPriority, err)
		})

		router.Post("/occurrences", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody admin2.OccurrencesParams
		}) {
			utils.WriteResultJSON(w, admin2.Occurrences(r, params.FromBody))
		})

		router.Get("/editors", func(w http.ResponseWriter, r *shared.AppContext) {
			utils.WriteResultJSON(w, admin2.GetContentEditors(r.TenantDatabase()))
		})

		router.Get("/preview/:id", func(w http.ResponseWriter, r *shared.AppContext, p struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID
			}
			FromDB content.Content
		}) {
			url, err := admin2.GetContentPreviewURL(r, p.FromDB)
			if err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			http.Redirect(w, r.Request(), url, http.StatusFound)
		})

		router.Get("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, admin2.GetByID(r, params.FromPath.ID))
		})

		/* CREATE */
		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody admin2.ContentDTO
		}) {
			utils.WriteResultJSON(w, admin2.CreateContent(r, params.FromBody))
		})

		router.Post("/:id/clone", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.CloneContent(r, params.FromDB))
		})

		/* UPDATE */
		router.Put("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromQuery commonModels.ReservableParams
			FromDB    content.Content
			FromBody  admin2.ContentDTO
		}) {
			utils.WriteResultJSON(w, admin2.UpdateContent(r, params.FromPath.ID, params.FromBody, params.FromQuery))
		})

		/* DELETE/RESTORE */
		router.Delete("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.SetStatus(r, params.FromPath.ID, false))
		})

		router.Patch("/:id/restore", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.SetStatus(r, params.FromPath.ID, true))
		})

		router.Patch("/:id/expire", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.Expire(r, params.FromPath.ID))
		})

		router.Get("/distributed-page/:parentID/:siteID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ParentID uuid.UUID `binding:"cm_uuid"`
				SiteID   uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, admin2.GetDistributedPage(r, params.FromPath.ParentID, params.FromPath.SiteID))
		})

		router.Put("/distributed-page/:parentID/:siteID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ParentID uuid.UUID `binding:"cm_uuid"`
				SiteID   uuid.UUID `binding:"cm_uuid"`
			}
			FromBody admin2.DistributedPageDTO
		}) {
			utils.WriteResultJSON(w, admin2.UpsertDistributedPage(r, params.FromPath.ParentID, params.FromPath.SiteID, params.FromBody))
		})

		router.Post("/resources", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody struct {
				Resources []string
			}
		}) {
			utils.WriteResultJSON(w, admin.GetEntitiesByResources(r.TenantDatabase(), params.FromBody.Resources))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())
	return r
}
