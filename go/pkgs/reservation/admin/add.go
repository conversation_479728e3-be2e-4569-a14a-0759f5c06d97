package admin

import (
	middlewares2 "contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/library/utils"
	"contentmanager/pkgs/reservation"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

type (
	baseRequiredFields struct {
		ID    uuid.UUID
		Table string
	}
	Params struct {
		baseRequiredFields
		reservation.ReservableParams
	}
)

func (b baseRequiredFields) Validate() bool {
	return b.Table != "" && b.ID != uuid.Nil
}

func AddAdminReservations(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {

	r.Group("/api/v1/reservable", func(router httpService.Router) {
		router.Put("", func(w http.ResponseWriter, r *shared.AppContext, p struct {
			bindauth.BindableParams
			FromBody Params
		}) {
			if !p.FromBody.Validate() {
				http.Error(w, "missing required table & entityID", http.StatusBadRequest)
				return
			}
			if p.FromBody.NextEditingSession == nil {
				http.Error(w, "bad request", http.StatusBadRequest)
				return
			}
			writeResult(w, UpdateEditingSession(r, p.FromBody.Table, p.FromBody.ID, p.FromBody.ReservableParams))
		})

		router.Delete("", func(w http.ResponseWriter, r *shared.AppContext, p struct {
			bindauth.BindableParams
			FromQuery Params
		}) {
			if !p.FromQuery.Validate() {
				http.Error(w, "missing required table & entityID", http.StatusBadRequest)
				return
			}
			if p.FromQuery.EditingSession == nil {
				http.Error(w, "bad request", http.StatusBadRequest)
				return
			}
			writeResult(w, EndEditingSession(r, p.FromQuery.Table, p.FromQuery.EditingSession, p.FromQuery.ID))
		})

		router.Delete("/lock", func(w http.ResponseWriter, r *shared.AppContext, p struct {
			bindauth.BindableParams
			FromQuery Params
		}) {
			if !p.FromQuery.Validate() {
				http.Error(w, "missing required table & entityID", http.StatusBadRequest)
				return
			}
			// Doesn't currently require an editing session, as you can remove an extended-lock without one as long as you are the current_editor
			writeResult(w, EndExtendedLock(r, p.FromQuery.Table, p.FromQuery.EditingSession, p.FromQuery.ID))
		})
		router.Delete("/lock/override", func(w http.ResponseWriter, r *shared.AppContext, p struct {
			bindauth.BindableParams
			FromQuery baseRequiredFields
		}) {
			if !p.FromQuery.Validate() {
				http.Error(w, "missing required table & entityID", http.StatusBadRequest)
				return
			}
			writeResult(w, OverrideExtendedLock(r, p.FromQuery.Table, p.FromQuery.ID))

		})

	}, middlewares2.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware())

	return r
}

func writeResult(w http.ResponseWriter, res result.IResult) {
	if res.IsError() {
		err := res.Unwrap()
		if errors.Is(err, reservation.ErrEditingSessionConflict) {
			utils.WriteStatusJSON(w, http.StatusConflict, map[string]interface{}{
				"Success":      false,
				"ErrorMessage": err.Error(),
				"Data":         res.GetData(),
			})
		} else {
			utils.WriteError(w, err)
		}
	} else {
		utils.WriteResultJSON(w, res)
	}
}
