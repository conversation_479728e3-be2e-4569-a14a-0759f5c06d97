package amzn

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/library/shared"
	"contentmanager/logging"
)

type UrlPather interface {
	GetUrlPaths() []string
}

func QueuePatherInvalidations(r *shared.AppContext, pather UrlPather) {
	go func(distributions driver.PgStringArray) {
		paths := pather.GetUrlPaths()
		logging.RootLogger().Info().Msgf("[QueuePatherInvalidations] Invalidating cache for paths %v", paths)

		QueueInvalidations(distributions, paths)
	}(r.Tenant().Distributions)
}
