package amzn

import (
	"sync"
)

type (
	InvalidationMap struct {
		sync.Mutex
		data map[string]map[string]any
	}
)

func NewInvalidationMap() *InvalidationMap {
	return &InvalidationMap{
		data: map[string]map[string]any{},
	}
}

func (im *InvalidationMap) Add(distributionID string, paths []string) {
	im.Lock()
	defer im.Unlock()
	v, ok := im.data[distributionID]
	if !ok {
		v = map[string]any{}
	}
	for _, path := range paths {
		v[path] = nil
	}
	im.data[distributionID] = v
}

func (im *InvalidationMap) Drain() map[string][]string {
	im.Lock()
	defer im.Unlock()
	scoped := map[string][]string{}
	for distributionID, paths := range im.data {
		collect := make([]string, 0, len(paths))
		for path := range paths {
			collect = append(collect, path)
		}
		scoped[distributionID] = collect
	}
	im.data = map[string]map[string]any{}
	return scoped
}
