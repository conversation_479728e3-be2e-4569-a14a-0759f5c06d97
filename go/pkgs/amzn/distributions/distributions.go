package distributions

//
//func mapTenantsToDistributions(tenancyDB *gorm.DB) error {
//	var domains []tenancyModels.Domain
//	if err := tenancyDB.Where("active").
//		Where("domain.domain not ilike '%.cmdesign.%'").
//		Find(&domains).Error; err != nil {
//		return err
//	}
//	domainToTenant := slicexx.ToMap(domains, func(v tenancyModels.Domain) string {
//		return v.Domain
//	}, func(v tenancyModels.Domain) uuid.UUID {
//		return v.TenantID
//	})
//
//	list, err := listDistributions()
//	if err != nil {
//		return err
//	}
//
//	newDistributions := map[uuid.UUID][]string{}
//	for _, dist := range list {
//		id := *dist.Id
//		for _, alias := range dist.Aliases.Items {
//			if d, ok := findMatch(*alias, domainToTenant); ok {
//				if _, ok := newDistributions[d]; !ok {
//					newDistributions[d] = []string{}
//				}
//				newDistributions[d] = append(newDistributions[d], id)
//				break
//			}
//		}
//	}
//
//	distributions = newDistributions
//	return nil
//}
//
//func listDistributions() ([]*cloudfront.DistributionSummary, error) {
//	var distributionSummaries []*cloudfront.DistributionSummary
//	input := &cloudfront.ListDistributionsInput{}
//
//	// Paginate through the results if there are more distributionSummaries
//	err := cf.ListDistributionsPages(input,
//		func(page *cloudfront.ListDistributionsOutput, lastPage bool) bool {
//			if page.DistributionList != nil {
//				distributionSummaries = append(distributionSummaries, page.DistributionList.Items...)
//			}
//			return !lastPage
//		})
//
//	if err != nil {
//		return nil, err
//	}
//	return distributionSummaries, nil
//}
//
//func findMatch(pattern string, domainToTenant map[string]uuid.UUID) (uuid.UUID, bool) {
//	if !strings.Contains(pattern, "*") {
//		tenantID, ok := domainToTenant[pattern]
//		return tenantID, ok
//	}
//
//	for k, v := range domainToTenant {
//		if policy.PatternMatch(pattern, k) {
//			return v, true
//		}
//	}
//	return uuid.Nil, false
//}
//
