package amzn

import (
	"contentmanager/library/utils/slicexx"
	"math/rand"
	"slices"
	"testing"
)

var (
	mediaPaths = []string{
		"/images/5",
		"/documents/1",
		"/folder/6",
		"/documents/2",
		"/documents/3",
		"/documents/4",
	}
	contentPaths = []string{
		"/news/1",
		"/news/2",
		"/events/1",
		"/events/2",
		"/some-page",
		"/about us",
		"/board-procedures",
	}
)

func testData() []string {
	var collect = append([]string{}, mediaPaths...)
	collect = append(collect, contentPaths...)

	rand.Shuffle(len(collect), func(i, j int) {
		collect[i], collect[j] = collect[j], collect[i]
	})
	return collect
}

func Test_SortAndTrim(t *testing.T) {
	res1 := sortAndFilter(testData(), 10)
	for _, path := range mediaPaths {
		if !slicexx.Contains(res1, path) {
			t.<PERSON>rrorf("Expected %s to be found in the returned slice", path)
		}
	}

	shouldNotBeModified := []string{
		"/news/1",
		"/image/2",
		"/events/3",
	}
	res2 := sortAndFilter(shouldNotBeModified, 10)
	if !slices.Equal(shouldNotBeModified, res2) {
		t.Errorf("Expected %v, got %v", shouldNotBeModified, res2)
	}
}
