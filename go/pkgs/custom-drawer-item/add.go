package custom_drawer_item

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/shared/result"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/settings"
	"encoding/json"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
)

var (
	CustomDrawerItemContentTypes = []commonModels.ContentType{commonModels.Page, commonModels.Event, commonModels.News, commonModels.Fragment}
	CustomDrawerItemSettingsName = "CustomDrawerItems"
	//CustomDrawerItemSettingsType = settings.CustomDrawerItem
)

type (
	CustomDrawerItem struct {
		ID uuid.UUID
		// e.g.: "/testroute"
		Route       string // UNIQUE
		Label       string
		IconName    string
		Description string
		ContentType commonModels.ContentType
		Templates   []uuid.UUID
		Structures  []uuid.UUID
		Roles       []uuid.UUID
		Tags        []uuid.UUID
		TagsMinMax  []int // [min, max]
		Active      bool
	}

	CustomDrawerItemDTO struct {
		// e.g.: "/testroute"
		Route       string // UNIQUE
		Label       string
		IconName    string
		Description string
		ContentType commonModels.ContentType
		Templates   []uuid.UUID
		Structures  []uuid.UUID
		Roles       []uuid.UUID
		Tags        []uuid.UUID
		TagsMinMax  []int // [min, max]
		Active      bool
	}
)

func (cdi CustomDrawerItem) isValid() error {
	ee := map[string]string{}
	if len(cdi.Route) < 3 {
		ee["Route"] = "Route must be at least 3 characters"
	} else {
		runes := []rune(cdi.Route)
		if runes[0] != '/' {
			ee["Route"] = "Route must start with a forward slash: \"/\""
		}
	}

	if len(cdi.Label) < 1 {
		ee["Label"] = "Label cannot be empty"
	}

	if len(cdi.Label) > 50 {
		ee["Label"] = "Label cannot be greater than 50 characters"
	}

	if !slicexx.Contains(CustomDrawerItemContentTypes, cdi.ContentType) {
		ee["ContentType"] = "Content Type must be one of: Page, News, Event, Fragment"
	}

	if len(cdi.TagsMinMax) > 2 {
		ee["TagsMinMax"] = "Tags min max should only contain who numbers [min, max]"
	}

	if len(ee) > 0 {
		return errx.NewValidationError(ee)
	}

	return nil
}

func AddCustomDrawerItem(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v1/custom-drawer-item", func(router httpService.Router) {
		router.Get("", func(w http.ResponseWriter, r *shared.AppContext) {
			utils.WriteResultJSON(w, GetAll(r))
		})
	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.AuthorizeMiddleware())

	r.Group("/api/v1/custom-drawer-item", func(router httpService.Router) {
		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody CustomDrawerItemDTO
		}) {
			utils.WriteResultJSON(w, Create(r, params.FromBody))
		})

		router.Put("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody CustomDrawerItem
		}) {
			utils.WriteResultJSON(w, Update(r, params.FromBody))
		})

		router.Put("/all", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody []CustomDrawerItem
		}) {
			utils.WriteResultJSON(w, UpdateAllItems(r, params.FromBody))
		})

		router.Delete("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, Delete(r, params.FromPath.ID))
		})
	}, middlewares.RequiresAuthenticationMiddleware(), middlewares.RequireAdminMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())

	return r
}

func getDefaultSettingsDTO(data json.RawMessage) settings.SettingsDTO {
	return settings.SettingsDTO{
		Name:        CustomDrawerItemSettingsName,
		Description: "",
		Type:        settings.CustomDrawerItem,
		Public:      false,
		Sites:       nil,
		Data:        data,
	}
}

func getSettingsDTOWithNewData(oldSettings settings.Settings, newData json.RawMessage) settings.SettingsDTO {
	return settings.SettingsDTO{
		Name:        oldSettings.Name,
		Description: oldSettings.Description,
		Type:        oldSettings.Type,
		Sites:       oldSettings.Sites,
		Public:      oldSettings.Public,
		Data:        newData,
	}
}

func getSettingsRecord(db *gorm.DB) (settings.Settings, error) {
	var oldSettings settings.Settings
	res := db.First(&oldSettings, "name = ?", CustomDrawerItemSettingsName)

	if res.Error != nil {
		return settings.Settings{}, res.Error
	}

	return oldSettings, nil
}

func getCustomDrawerItemsFromSettings(_settings settings.Settings) ([]CustomDrawerItem, error) {
	var oldSettingsData []CustomDrawerItem
	err := json.Unmarshal(_settings.Data, &oldSettingsData)
	if err != nil {
		return nil, err
	}

	return oldSettingsData, nil
}

func GetAll(r *shared.AppContext) result.Result[[]CustomDrawerItem] {
	oldSettings, err := getSettingsRecord(r.TenantDatabase())
	if err != nil {
		return result.Success([]CustomDrawerItem{})
	}

	currentCustomDrawerItems, err := getCustomDrawerItemsFromSettings(oldSettings)
	return result.Check(currentCustomDrawerItems, err)
}

// create a CDI
func Create(r *shared.AppContext, newItemDTO CustomDrawerItemDTO) result.Result[string] {
	newItem := CustomDrawerItem{
		ID:          uuid.NewV4(),
		Route:       newItemDTO.Route,
		Label:       newItemDTO.Label,
		IconName:    newItemDTO.IconName,
		Description: newItemDTO.Description,
		ContentType: newItemDTO.ContentType,
		Templates:   newItemDTO.Templates,
		Structures:  newItemDTO.Structures,
		Roles:       newItemDTO.Roles,
		Tags:        newItemDTO.Tags,
		TagsMinMax:  newItemDTO.TagsMinMax,
		Active:      newItemDTO.Active,
	}

	newItemErrors := newItem.isValid()
	if newItemErrors != nil {
		return result.Check(newItemErrors.Error(), newItemErrors)
	}

	oldSettings, err := getSettingsRecord(r.TenantDatabase())
	if err != nil {
		newData, _ := json.Marshal([]CustomDrawerItem{newItem})
		settings.Create(r, getDefaultSettingsDTO(newData))
		return result.Success("Success")
	}

	currentCustomDrawerItems, err := getCustomDrawerItemsFromSettings(oldSettings)
	if err != nil {
		// data is bad...
		return result.Error(errors.New("Something went wrong. Contact support."), "error")
	}

	// if the route already exists
	_, found := slicexx.FindFirst(currentCustomDrawerItems, func(cti CustomDrawerItem) bool {
		return cti.Route == newItem.Route
	})

	if found {
		return result.Error(errors.New("Another item with the same route exists. Please try again."), "Error")
	}

	updatedCustomDrawerItems := append(currentCustomDrawerItems, newItem)

	rawUpdatedCustomDrawerItems, _ := json.Marshal(updatedCustomDrawerItems)
	res := settings.Update(r, oldSettings.ID, getSettingsDTOWithNewData(oldSettings, rawUpdatedCustomDrawerItems))

	if res.IsError() {
		return result.Error(errors.New("Something went wrong. Contact support."), "Error")
	}

	return result.Success("Success")
}

// update a CDI
func Update(r *shared.AppContext, updatedItem CustomDrawerItem) result.Result[string] {
	newItemErrors := updatedItem.isValid()
	if newItemErrors != nil {
		return result.Check(newItemErrors.Error(), newItemErrors)
	}

	oldSettings, err := getSettingsRecord(r.TenantDatabase())
	if err != nil {
		newData, _ := json.Marshal([]CustomDrawerItem{updatedItem})
		settings.Create(r, getDefaultSettingsDTO(newData))
		return result.Success("Success")
	}

	currentCustomDrawerItems, err := getCustomDrawerItemsFromSettings(oldSettings)
	if err != nil {
		// data is bad...
		return result.Error(errors.New("Something went wrong. Contact support."), "error")
	}

	_, duplicateRouteFound := slicexx.FindFirst(currentCustomDrawerItems, func(cti CustomDrawerItem) bool {
		return cti.ID != updatedItem.ID && cti.Route == updatedItem.Route
	})

	if duplicateRouteFound {
		return result.Error(errors.New("Another item with the same route exists. Please try again."), "Error")
	}

	_, existingItem := slicexx.FindFirst(currentCustomDrawerItems, func(cti CustomDrawerItem) bool {
		return cti.ID == updatedItem.ID
	})

	if !existingItem {
		return result.Error(errors.New("record not found"), "error")
	}

	updatedCustomDrawerItems, replaced := slicexx.Replace(currentCustomDrawerItems, func(cti CustomDrawerItem) bool {
		return cti.ID == updatedItem.ID
	}, updatedItem)

	if !replaced {
		return result.Error(errors.New("Something went wrong. Contact support."), "error")
	}

	rawUpdatedCustomDrawerItems, _ := json.Marshal(updatedCustomDrawerItems)
	res := settings.Update(r, oldSettings.ID, getSettingsDTOWithNewData(oldSettings, rawUpdatedCustomDrawerItems))
	if res.IsError() {
		return result.Error(errors.New("Something went wrong. Contact support."), "Error")
	}

	return result.Success("Success")
}

func Delete(r *shared.AppContext, id uuid.UUID) result.Result[string] {
	oldSettings, err := getSettingsRecord(r.TenantDatabase())

	if err != nil {
		return result.Error(errors.New("Something went wrong. Contact support."), "error")
	}

	currentCustomDrawerItems, err := getCustomDrawerItemsFromSettings(oldSettings)
	if err != nil {
		// data is bad...
		return result.Error(errors.New("Something went wrong. Contact support."), "error")
	}

	updatedCustomDrawerItems := slicexx.RemoveWithFunc(currentCustomDrawerItems, func(cdi CustomDrawerItem) bool {
		return cdi.ID == id
	})

	rawUpdatedCustomDrawerItems, _ := json.Marshal(updatedCustomDrawerItems)
	res := settings.Update(r, oldSettings.ID, getSettingsDTOWithNewData(oldSettings, rawUpdatedCustomDrawerItems))
	if res.IsError() {
		return result.Error(errors.New("Something went wrong. Contact support."), "Error")
	}

	return result.Success("Success")
}

func UpdateAllItems(r *shared.AppContext, ctis []CustomDrawerItem) result.Result[string] {
	for _, cti := range ctis {
		newItemErrors := cti.isValid()
		if newItemErrors != nil {
			return result.Check(newItemErrors.Error(), newItemErrors)
		}
	}

	newData, _ := json.Marshal(ctis)

	oldSettings, err := getSettingsRecord(r.TenantDatabase())
	if err != nil {
		settings.Create(r, getDefaultSettingsDTO(newData))
		return result.Success("Success")
	}

	res := settings.Update(r, oldSettings.ID, getSettingsDTOWithNewData(oldSettings, newData))
	if res.IsError() {
		return result.Error(errors.New("Something went wrong. Contact support."), "Error")
	}

	return result.Success("Success")
}
