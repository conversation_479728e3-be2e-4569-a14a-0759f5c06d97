package adminMedia

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	adminDataaccess "contentmanager/library/tenant/admin/dataaccess"
	"contentmanager/library/tenant/admin/services"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/common/services"
	"contentmanager/library/utils"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/slicexx"
	"contentmanager/logging"
	"contentmanager/pkgs/amzn"
	"contentmanager/pkgs/auth/permissions/evaluators"
	"contentmanager/pkgs/image_crop_size"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/satori/go.uuid"
	"io"
	"net/http"
	"net/url"
	"time"
)

type Image struct {
	ID               uuid.UUID          `json:"id"`
	DepartmentID     *uuid.UUID         `json:"department_id"`
	Sites            []uuid.UUID        `json:"sites"`
	Type             string             `json:"type"`
	ContentID        *uuid.UUID         `json:"content_id"`
	Tags             []uuid.UUID        `json:"tags"`
	Filename         string             `json:"filename"`
	Alt              string             `json:"alt"`
	Thumbnail        string             `json:"thumbnail"`
	Data             string             `json:"data"`
	ImageCropSizeIDs driver.PgUUIDArray `json:"image_crop_size_ids"`
	//Origin       *string      `json:"origin"`
}

func (c Image) GetSites() []uuid.UUID {
	return c.Sites
}

func (c Image) GetDepartmentID() *uuid.UUID {
	return c.DepartmentID
}

func (c Image) GetType() string {
	return c.Type
}

func (c Image) GetScopeEntity() string {
	return "cm.image"
}

func (c Image) GetID() uuid.UUID {
	return c.ID
}

func (c Image) GetUrlPaths() []string {
	return []string{
		fmt.Sprintf("/images/%s", c.ID),
		fmt.Sprintf("/thumbnails/%s", c.ID),
		fmt.Sprintf("/images/%s", url.QueryEscape(c.Filename))}
}

var _ commonModels.IContent = (*Image)(nil)
var _ commonModels.IEntity = (*Image)(nil)

func AdminMedia_Get(w http.ResponseWriter, r *shared.AppContext) {
	var params adminDataaccess.Params
	if err := binding.MapFromMaps(&params, r.Maps()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	utils.WriteResultJSON(w, adminDataaccess.SearchMedia(r, params))
}

func Image_DELETE(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	var media commonModels.Media
	id, idErr := uuid.FromString(p["id"])
	if idErr != nil {
		utils.WriteResponseJSON(w, nil, idErr)
		return
	}
	if err := evaluators.ForActionByID(r, &media, id, "delete"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	media.Active = false
	media.Deleted = time.Now()

	if _, err := adminServices.UpdateMedia(r.TenantDatabase(), media); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
}

func UploadImage_POST(w http.ResponseWriter, r *shared.AppContext) {
	var image Image
	if err := evaluators.ForCreate(r, &image); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if err := commonServices.UploadMedia(commonModels.UploadMedia{
		ID:        image.ID,
		Data:      image.Data,
		Thumbnail: image.Thumbnail,
	}, r.TenantID()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	now := time.Now()
	if _, err := adminServices.CreateMedia(r.TenantDatabase(), commonModels.Media{
		ID:           image.ID,
		Filename:     image.Filename,
		Sites:        image.Sites,
		DepartmentID: image.DepartmentID,
		ContentId:    uuid.NullUUID{UUID: converters.FromPointer(image.ContentID), Valid: image.ContentID != nil},
		Type:         commonModels.MediaType(image.Type),
		Tags:         image.Tags,
		Alt:          image.Alt,
		Created:      now,
		Updated:      now,
		Active:       true,
	}); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	utils.WriteResponseJSON(w, nil, nil)
}

func EditImageData_PUT(w http.ResponseWriter, r *shared.AppContext) {
	var image Image
	var media commonModels.Media
	if err := evaluators.ForUpdate(r, &image, &media); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	media.Sites = image.Sites
	media.DepartmentID = image.DepartmentID
	media.ContentId = uuid.NullUUID{UUID: converters.FromPointer(image.ContentID), Valid: image.ContentID != nil}
	media.Tags = image.Tags
	media.Filename = image.Filename
	media.Alt = image.Alt
	media.Updated = time.Now()

	if len(image.Data) > 0 && len(image.Thumbnail) > 0 {
		// replace image
		if err := commonServices.UploadMedia(commonModels.UploadMedia{
			ID:        image.ID,
			Data:      image.Data,
			Thumbnail: image.Thumbnail,
		}, r.TenantID()); err != nil {
			utils.WriteResponseJSON(w, nil, err)
			return
		}

		media.DirtyImageCropSizeIds = media.ImageCropSizeIDs

		go func(tenantID uuid.UUID, image Image) {
			logging.RootLogger().Info().Msgf("[CreateInvalidationForTenant] Invalidating image cache %s", image.ID)

			amzn.QueuePatherInvalidations(r, image)

			logging.RootLogger().Info().Msgf("[CreateInvalidationForResizer] Invalidating image cache %s", image.ID)
			if _, err := amzn.CreateInvalidationForResizer(
				[]string{
					fmt.Sprintf("/%s*", image.ID),
					fmt.Sprintf("/%s*", url.QueryEscape(image.Filename))}); err != nil {
				logging.RootLogger().Warn().Err(err).Msg("[CreateInvalidationForResizer] Failed to create invalidation")
			}
		}(r.TenantID(), image)
	}

	if _, err := adminServices.UpdateMedia(r.TenantDatabase(), media); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	utils.WriteResponseJSON(w, nil, nil)
}

type ImageCropPutRequestBody struct {
	Data string `json:"data"`
}

func ImageCrop_PUT(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := p.GetUUID("id")
	cropID := p["cropID"]

	if err != nil {
		return
	}

	body, err := io.ReadAll(r.Request().Body)
	if err != nil {
		panic(err)
	}
	var data ImageCropPutRequestBody
	if err = json.Unmarshal(body, &data); err != nil {
		return
	}

	var originalImage commonModels.Media
	if err := r.TenantDatabase().Table(originalImage.TableName()).Where("id = ?", id).First(&originalImage).Error; err != nil {
		logging.RootLogger().Info().Msgf("[CreateInvalidationForTenant] error finding image %s", err)
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var cropSize image_crop_size.ImageCropSize
	if err := r.TenantDatabase().Table(cropSize.TableName()).Where("id = ?", cropID).First(&cropSize).Error; err != nil {
		utils.WriteResponseJSON(w, nil, errors.New("Crop size with this id does not exist:"+cropID))
		return
	}

	if len(data.Data) == 0 {
		utils.WriteResponseJSON(w, nil, errors.New("crop data cannot be empty"))
		return
	}

	if err := commonServices.UploadCrop(r.TenantID(), originalImage.ID, cropSize.ID, data.Data); err != nil {
		utils.WriteResponseJSON(w, nil, errors.New("failed to upload image"))
		return
	}

	if slicexx.Contains(originalImage.ImageCropSizeIDs, cropSize.ID) == false {
		originalImage.ImageCropSizeIDs = append(originalImage.ImageCropSizeIDs, cropSize.ID)
	}

	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if slicexx.Contains(originalImage.DirtyImageCropSizeIds, cropSize.ID) {
		originalImage.DirtyImageCropSizeIds = slicexx.Remove(originalImage.DirtyImageCropSizeIds, cropSize.ID)
	}

	originalImage.Updated = time.Now()

	if _, err := adminServices.UpdateMedia(r.TenantDatabase(), originalImage); err != nil {
		utils.WriteResponseJSON(w, nil, errors.New("failed to update image"))
		return
	}

	//go func(tenantID uuid.UUID, cropId uuid.UUID, factory multitenancy.AccessorFactory) {
	//	logging.RootLogger().Info().Msgf("[CreateInvalidationForTenant] Invalidating image cache %s", cropId)
	//	mta := factory.Accessor(context.Background())
	//
	//	if _, err := amzn.CreateInvalidationForTenant(mta.TenancyDB(), tenantID,
	//		[]string{
	//			fmt.Sprintf("/images/%s*", cropId)}); err != nil {
	//		logging.RootLogger().Warn().Err(err).Msgf("[CreateInvalidationForTenant] Failed to create invalidation for imageID %s in tenantID %s", cropId, tenantID)
	//	}
	//
	//	logging.RootLogger().Info().Msgf("[CreateInvalidationForResizer] Invalidating image cache %s", cropId)
	//	if _, err := amzn.CreateInvalidationForResizer(
	//		[]string{
	//			fmt.Sprintf("/%s*", cropId)}); err != nil {
	//		logging.RootLogger().Warn().Err(err).Msg("[CreateInvalidationForResizer] Failed to create invalidation")
	//	}
	//}(r.TenantID(), cropId, factory)

	utils.WriteResponseJSON(w, nil, nil)
}
