package navigation

import (
	"contentmanager/library/tenant/common/models"
)

type NavigationContent struct {
	commonModels.Navigation
	ParentID string `json:"parent_id" gorm:"column:parent_id;type:string;"`
	Title    string `json:"title" gorm:"column:title;type:string"`
	Route    string `json:"route" gorm:"column:route;type:string"`
}

// test
type AdminNavigationWithContent struct {
	commonModels.Navigation
	Content  commonModels.Content `json:"content"`
	ParentId string               `gorm:"<-:false;"`
	// TODO => is this unnecessary
	//  admin/DataAccess/content_admin.go: SaveContent method deletes from navigation where site doesn't overlap
	HasSiteIdOverlap bool `gorm:"<-:false;column:has_site_id_overlap;"`
	HasPermission    bool `gorm:"-"`
}

func (NavigationContent) TableName() string {
	return "navigation"
}
func (c NavigationContent) String() string {
	return c.Title
}
func (AdminNavigationWithContent) TableName() string {
	return "navigation"
}
func (c AdminNavigationWithContent) String() string {
	return c.Content.Title
}
