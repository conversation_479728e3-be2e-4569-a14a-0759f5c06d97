package navigation

import (
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/ltree"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetNavigationDataByIDAndSite(dbCon *gorm.DB, contentId uuid.UUID, siteId uuid.NullUUID, ignoreActive bool) (commonModels.Navigation, error) {
	var navigation commonModels.Navigation
	dbQuery := dbCon

	dbQuery = dbQuery.
		Where(" content_id = ? ", contentId).
		Where(" site_id = ? ", siteId)
	if !ignoreActive {
		dbQuery = dbQuery.
			Where(" active = true ")
	}

	if err := dbQuery.
		First(&navigation).
		Error; err != nil {
		return navigation, err
	}

	return navigation, nil
}

func GetAdminNavigationResults(dbCon *gorm.DB, siteId uuid.UUID) ([]AdminNavigationWithContent, error) {
	var navigation = make([]AdminNavigationWithContent, 0)
	err := dbCon.Model(&navigation).
		Select(" navigation.*, subltree(navigation.path, 0, nlevel(navigation.path) -1 ) as parent_id, ? = any(c.sites) as has_site_id_overlap ", siteId).
		Joins(" INNER JOIN content c ON navigation.content_id = c.id").
		Where(" site_id = ?", siteId).
		Where(" navigation.active = true ").
		Preload("Content").
		Find(&navigation).Error

	return navigation, err
}

func GetAdminNavigationByDepartmentAndSiteId(dbQuery *gorm.DB, siteId uuid.UUID, departmentId uuid.UUID) ([]AdminNavigationWithContent, error) {
	var navigation = make([]AdminNavigationWithContent, 0)
	dbQuery.Model(&navigation).
		Select(" navigation.*, '' as parent_id ").
		Joins(" INNER JOIN content c ON navigation.content_id = c.id").
		Where(" site_id = ?", siteId).
		Where(" c.active = true ").
		Where(" navigation.active = true ")
	if departmentId != uuid.Nil {
		dbQuery = dbQuery.Where("navigation.department_id = ?", departmentId)
	}
	if err := dbQuery.Preload("Content").Find(&navigation).Error; err != nil {
		return navigation, err
	}
	for i, ref := range navigation {
		navigation[i].ParentId = ltree.MustAncestor(ref.Path)
	}

	return navigation, nil
}

func GetNavigationByContentId(dbCon *gorm.DB, contentId uuid.UUID) ([]AdminNavigationWithContent, error) {
	var n = make([]AdminNavigationWithContent, 0)
	return n, dbCon.
		Where("content_id = ?", contentId).
		Where("active = true").
		Find(&n).
		Error
}

//func CreateNavigation(dbCon *gorm.DB, queryModel commonModels.Navigation) (commonModels.Navigation, error) {
//	dbQuery := dbCon
//
//	if len(queryModel.Type) == 0 {
//		queryModel.Type = commonModels.PageRoute
//	}
//	queryModel.Active = true
//
//	if err := dbQuery.Create(&queryModel).Error; err != nil {
//		return queryModel, err
//	}
//	return queryModel, nil
//}
//func DeleteNavigation(dbCon *gorm.DB, queryModel commonModels.Navigation) (commonModels.Navigation, error) {
//	queryModel.Active = false
//	return ni.SaveNavigation(dbCon, queryModel)
//}
//func SaveNavigation(dbCon *gorm.DB, queryModel commonModels.Navigation) (commonModels.Navigation, error) {
//	dbQuery := dbCon
//	if len(queryModel.Type) == 0 {
//		queryModel.Type = commonModels.PageRoute
//	}
//	if err := dbQuery.Save(&queryModel).Error; err != nil {
//		return queryModel, err
//	}
//	return commonModels.Navigation{}, nil
//}
