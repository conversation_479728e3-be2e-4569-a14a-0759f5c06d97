package search

import (
	"contentmanager/library/shared"
	hbsHelpers "contentmanager/library/templates/hbs_helpers"
	commonModels "contentmanager/library/tenant/common/models"
	publicDataaccess "contentmanager/library/tenant/public/dataaccess"
	publicModels "contentmanager/library/tenant/public/models"
	publicServices "contentmanager/library/tenant/public/services"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/tenant/public/viewmodels"
	"contentmanager/pkgs/htmlxx"
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"strings"
)

/*
*	publicServices.SearchTemplate can be found at /go/lib/tenant/public/services/templates
 */

type (
	ISearch interface {
		GetAndCompileSearchResults(r *shared.AppContext, params QueryParams) (string, error)
		GetSearchResults(r *shared.AppContext, params QueryParams) ([]SearchResultsViewModel, error)
	}
	searchServiceInt struct{}

	SearchResultsViewModel struct {
		Type    commonModels.ContentType
		Title   string
		Content string
		Route   string
	}
	SearchViewModel struct {
		CurrentTitle        string
		SearchTerm          string
		SanitizedSearchTerm string
		SearchResults       []SearchResultsViewModel
		Error               error
	}
)

func (si searchServiceInt) GetSearchResults(r *shared.AppContext, params QueryParams) ([]SearchResultsViewModel, error) {
	if len(params.SearchTerm) == 0 {
		return nil, errors.New("invalid search term")
	}
	var searchChain, err = ISearchDataAccessAdapter().GetSearchResults(r.TenantDatabase(), params)
	if err != nil {
		return nil, err
	}
	return processSearchChain(r, searchChain)
}
func (si searchServiceInt) GetAndCompileSearchResults(r *shared.AppContext, params QueryParams) (string, error) {
	var searchViewModel SearchViewModel
	if len(params.SearchTerm) == 0 {
		searchViewModel.CurrentTitle = "Search"
		searchViewModel.SearchTerm = "No Search Term Provided"
		searchViewModel.SanitizedSearchTerm = "No Search Term Provided"
		searchViewModel.SearchResults = []SearchResultsViewModel{}
		searchViewModel.Error = errors.New("invalid search term")
	} else {
		results, err := si.GetSearchResults(r, params)
		searchViewModel.CurrentTitle = params.SearchTerm
		searchViewModel.SearchTerm = params.SearchTerm
		searchViewModel.SanitizedSearchTerm = params.SearchTerm
		searchViewModel.Error = err
		searchViewModel.SearchResults = results
	}
	return publicServices.CompileTemplate(r, searchViewModel, publicServices.SearchTemplate)
}

// processSearchChain
// This is a performance issue on top of the already costly union query in GetAndCompileSearchResults
func processSearchChain(r *shared.AppContext, searchChain []commonModels.Content) ([]SearchResultsViewModel, error) {
	var helpers = hbsHelpers.NewHbsHelpers(r)
	var results = []SearchResultsViewModel{}
	site, err := r.SiteByID(r.CurrentSiteID())
	//site, err := tenancyServices.ISiteAdapter().GetSiteById(r.TenantID(), r.CurrentSiteID())
	if err != nil {
		return results, errors.New("could not determine site")
	}
	// BaseViewModel is only used in the ProcessSearchChain - publicServices.CompileTemplate provides
	// the required BaseViewModel for executing the Master / Wrapper templates.
	var baseViewModel = &viewmodels.BaseContentPage{
		Timezone:          r.Timezone(),
		Account:           r.Account().PublicAccount(),
		Content:           publicModels.ContentForHandlebars{},
		Site:              site.ToViewModel(),
		News:              []publicModels.ContentForHandlebars{},
		Dct:               json.RawMessage{},
		NavigationContent: publicModels.ContentForHandlebars{},
		CurrentTitle:      "Search Results",
	}

	for _, item := range searchChain {
		if item.PageLayout == commonModels.Dct {
			// TODO => Refactor DCT && dbQuery in For loop
			dctContentChain, _ := publicDataaccess.GetContentData(r.TenantDatabase(), r.CurrentSiteID(), item.Route, false)
			if len(dctContentChain) > 1 {
				dctParentTemplate := dctContentChain[len(dctContentChain)-2]
				currentContent := dctContentChain[len(dctContentChain)-1]

				bestTemplate := currentContent.ContentStructure.Template
				if len(bestTemplate) == 0 {
					bestTemplate = dctParentTemplate.Content
				}

				contentStr := ""

				resultViewModel := baseViewModel
				resultViewModel.Content = dctParentTemplate
				resultViewModel.Dct = currentContent.Data
				resultViewModel.CurrentTitle = currentContent.Title
				resultViewModel.CurrentContent = currentContent
				if contentTemplate, err := handlebars.Parse(bestTemplate); err == nil {
					if contentStr, err = contentTemplate.ExecWithHelpers(resultViewModel, helpers); err == nil {
						contentStr = htmlxx.ExtractTextFromHTMLForIndexingOrEmpty(contentStr)
						contentLength := map[bool]int{true: 400, false: len(contentStr)}[len(contentStr) > 400]
						if contentLength == 400 {
							contentStr = strings.TrimSpace(contentStr[:contentLength]) + "..."
						}
					}
				}

				t := html.UnescapeString(currentContent.Title)
				c := html.UnescapeString(contentStr)
				if strings.HasPrefix(c, t) {
					c = strings.TrimPrefix(c, t)
				}

				results = append(results, SearchResultsViewModel{
					Type:    currentContent.Type,
					Title:   t,
					Content: c,
					Route:   currentContent.Route,
				})
			}
		} else if item.PageLayout == "document" {
			var route string
			if item.Type == "folder" {
				route = fmt.Sprintf("/folder/%s", item.ID)
			} else {
				route = fmt.Sprintf("/documents/%s", item.ID)
			}
			results = append(results, SearchResultsViewModel{
				Type:    item.Type,
				Title:   html.UnescapeString(item.Title),
				Content: "",
				Route:   route,
			})
		} else {
			resultViewModel := baseViewModel
			resultViewModel.Content = publicModels.FromContent(item)
			resultViewModel.CurrentTitle = item.Title
			if contentTemplate, err := handlebars.Parse(item.Content); err == nil {
				if contentStr, err := contentTemplate.ExecWithHelpers(resultViewModel, helpers); err == nil {
					contentStr = htmlxx.ExtractTextFromHTMLForIndexingOrEmpty(contentStr)
					contentLength := map[bool]int{true: 400, false: len(contentStr)}[len(contentStr) > 400]
					if contentLength == 400 {
						contentStr = strings.TrimSpace(contentStr[:contentLength]) + "..."
					}
					results = append(results, SearchResultsViewModel{
						Type:    item.Type,
						Title:   html.UnescapeString(item.Title),
						Content: html.UnescapeString(contentStr),
						Route:   item.Route,
						//Rank:    item.Rank,
					})
				}
			}
		}
	}
	return results, nil
}

func ISearchAdapter() ISearch {
	return &searchServiceInt{}
}
