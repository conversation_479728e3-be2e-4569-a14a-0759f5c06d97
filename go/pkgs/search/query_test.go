package search

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/tests"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"testing"
	"time"
)

var testCases = []struct {
	params    QueryParams
	total     int
	documents int
}{
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "content",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     25,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "document content",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     14,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        []string{"news", "event"},
			SearchTerm:   "document content",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     10,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        []string{"event"},
			SearchTerm:   "document content",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     5,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        []string{"document"},
			SearchTerm:   "document content",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     0,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "document event",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     10,
		documents: 5,
	},
	{
		params: QueryParams{
			Types:        []string{"document"},
			SearchTerm:   "document event",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     5,
		documents: 5,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "document page",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     9,
		documents: 5,
	},
	{
		params: QueryParams{
			Types:        []string{"page"},
			SearchTerm:   "document page",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     4,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "document",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     25,
		documents: 18,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "news",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     13,
		documents: 4,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "summer school",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     25,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "\"summer school\"",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     18,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "\"summer school\" -2022",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     9,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "\"summer school\" -2022 -2023",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     0,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "summer school -2022 -2023",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     9,
		documents: 0,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "anti-racism policy",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     18,
		documents: 18,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "anti racism policy",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     18,
		documents: 18,
	},
	{
		params: QueryParams{
			Types:        nil,
			SearchTerm:   "\"PDSB_Anti-Racism_Policy.pdf\"",
			SiteID:       site,
			PrivacyLevel: 0,
		},
		total:     6,
		documents: 6,
	},
}

func Test_GetSearchResults(t *testing.T) {
	// Arrange
	// ctx := tests.InitLogging("GetSearchResults")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	acc := seedAccount(db)
	seedContent(db, acc)
	seedDocuments(db)

	sut := searchDataAccessInt{}

	// Act
	for _, tc := range testCases {
		res, err := sut.GetSearchResults(db /*.WithContext(ctx) */, tc.params)
		t.Log(SanitizeSearchTerm(tc.params.SearchTerm))

		// Assert
		if (err != nil && tc.total > 0) || (err == nil && tc.total == 0) {
			t.Error(err)
		}

		if len(res) != tc.total {
			t.Errorf("Expected %d results to be returned but got %d", tc.total, len(res))
		}
		docsNum := slicexx.Count(res, func(c commonModels.Content) bool {
			return c.Type == "document"
		})
		if docsNum != tc.documents {
			t.Errorf("Expected %d documents to be returned but got %d", tc.documents, docsNum)
		}
	}
}

var site = uuid.NewV4()

func seedAccount(db *gorm.DB) identity.Account {
	acc := identity.Account{
		Firstname:    "First",
		Lastname:     "Last",
		Email:        "<EMAIL>",
		PrivacyLevel: 3,
		Active:       true,
		IsAdmin:      true,
	}
	if err := db.Create(&acc).Error; err != nil {
		panic(err)
	}
	return acc
}
func seedContent(db *gorm.DB, acc identity.Account) {
	now := time.Now()
	phrases := []string{
		"summer school 2022",
		"summer school 2023",
		"summer lorem ipsum school",
	}
	types := []commonModels.ContentType{commonModels.Page, commonModels.Event, commonModels.News}
	for i := 0; i < 27; i++ {
		t := types[i%3]
		phrase := phrases[i%3]
		word := string(t)
		doc := ""
		if i > 12 {
			doc = "document"
		}

		c := commonModels.Content{
			Type:         t,
			Title:        word,
			Content:      fmt.Sprintf("content %s %s %d %s", word, doc, i, phrase),
			Route:        fmt.Sprintf("/%s/%d", word, i),
			PageLayout:   "HTML",
			Created:      now,
			Updated:      now,
			PrivacyLevel: 0,
			Owner:        acc.ID,
			Publisher:    acc.ID,
			Approved:     true,
			Active:       true,
			Sites:        []uuid.UUID{site},
			PublishAt:    &now,
			Settings:     []byte(`{"enddate": "2022-10-01T05:59:59.000Z", "seoTitle": "", "startdate": "2022-09-30T06:00:00.000Z", "seoDescription": ""}`),
		}

		if err := db.Create(&c).Error; err != nil {
			panic(err)
		}
	}
}

func seedDocuments(db *gorm.DB) {
	words := []string{"page", "event", "news", "document"}
	words2 := []string{"anti-racism policy", "anti racism policy", "PDSB_Anti-Racism_Policy.pdf"}
	for i := 0; i < 18; i++ {
		word := words[i%len(words)]
		word2 := words2[i%len(words2)]
		id := uuid.NewV4()
		doc := commonModels.Document{
			ID:           id,
			Filename:     fmt.Sprintf("document %s document%d %s", word, i, word2),
			PrivacyLevel: 0,
			Type:         commonModels.File,
			Sites:        []uuid.UUID{site},
			Created:      time.Now(),
			Updated:      time.Now(),
			Active:       true,
		}
		if err := db.Create(&doc).Error; err != nil {
			panic(err)
		}
	}
}
