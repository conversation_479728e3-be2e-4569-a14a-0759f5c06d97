package settings

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/tests"
	"contentmanager/tests/db_seed"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"testing"
)

func Test_ValidateOutlookSettings(t *testing.T) {
	db, dispose := tests.InitTenantDB()
	defer dispose()

	ID, err := seedTemplate(db)
	if err != nil {
		t.Fatal(err)
	}

	withValidID := db_seed.GetImportConfig("123", ID)
	_, err = ValidateEdsbySettings(db, utils.InterfaceToJsonb(withValidID.FromSettingsData))
	if err != nil {
		t.Error(err)
	}
	withoutValidID := db_seed.GetImportConfig("234", uuid.Nil)
	_, err = ValidateEdsbySettings(db, utils.InterfaceToJsonb(withoutValidID.FromSettingsData))
	if err == nil {
		t.Error(err)
	}
}

func seedTemplate(db *gorm.DB) (uuid.UUID, error) {
	acc := identity.Account{
		Email:        "<EMAIL>",
		PrivacyLevel: 2,
		Active:       true,
	}
	if err := db.Create(&acc).Error; err != nil {
		return uuid.Nil, err
	}
	ID := uuid.NewV4()
	tmpl := commonModels.Content{
		ID:        ID,
		Type:      "template",
		Owner:     acc.ID,
		Publisher: acc.ID,
		Content:   "<div></div>",
		Path:      utils.SanitizeLTree(ID.String()),
		Active:    true,
		Settings: utils.InterfaceToJsonb(map[string]interface{}{
			"classification": "[\"event\", \"news\"]",
		}),
	}
	return tmpl.ID, db.Create(&tmpl).Error
}
