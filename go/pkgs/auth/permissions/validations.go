package permissions

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
	"time"
)

// ValidateRoute validates that the route is not overlapped with another content.
//
//	Restore/Clone functionality does not require validation because it sets `publish_at` to null.
func ValidateRoute(tenantDB *gorm.DB, content commonModels.IContentRoute) error {
	route := content.GetRoute()
	contentType := content.GetType()

	// skip validation for external links
	if contentType == "external_link" {
		return nil
	}
	if (contentType == "news" || contentType == "event") && strings.HasPrefix(route, "http") {
		return nil
	}

	// skip validation for unpublished contents
	if !content.GetPublished() {
		return nil
	}

	if len(route) == 0 {
		if contentType == "distributed_page" ||
			contentType == "alert" ||
			contentType == "template" ||
			contentType == "navigation" ||
			contentType == "fragment" ||
			len(contentType) == 0 {
			return nil
		} else {
			return errors.New("Route is required. ")
		}
	}

	type Content struct {
		ID    uuid.UUID            `gorm:"column:id"`
		Title string               `gorm:"column:title"`
		Route string               `gorm:"column:route"`
		Sites dbDriver.PgUUIDArray `json:"sites" gorm:"column:sites;type:uuid[]"`
	}

	var contents []Content
	if err := tenantDB.Table("content").Select("id, title, route, sites").
		Where("active").
		Where(pgxx.PublishPeriod(time.Now().UTC(), "", false)).
		Where("type NOT IN('external_link')").
		Where("lower(route) = lower(?)", route).
		Where(pgxx.ArrayHasAny("sites", content.GetSites())).
		Find(&contents).Error; err != nil {
		return err
	}
	// 0 for a new content
	if len(contents) == 0 {
		return nil
	}
	if len(contents) == 1 && contents[0].ID == content.GetID() {
		return nil
	}

	conflictContents := slicexx.Filter(contents, func(c Content) bool {
		return c.ID != content.GetID()
	})
	conflictContentsTitles := slicexx.Select(conflictContents, func(c Content) string {
		return c.Title
	})
	return errors.New(fmt.Sprintf("Route is already taken. Conflicting content title(s): %s", slicexx.JoinAsStrings(conflictContentsTitles, ", ")))
}

// ValidateSitesForSharable validates that the Sites array in the content are valid.
//
//	For departments, it checks that the department exists and sites are a subset of Hosts of the department.
//	For regular content, it checks that the sites are exist and none of them is a department (for now we can't share content with departments).
func ValidateSitesForSharable(r *shared.AppContext, content auth.Sharable) error {
	contentSites := content.GetSites()
	if contentSites == nil {
		return nil
	}

	if len(contentSites) == 0 {
		return errors.New("Sites array is empty. Select sites to share content with. ")
	}

	sites, err := r.Sites()
	if err != nil {
		return err
	}

	departmentID := content.GetDepartmentID()
	if departmentID != nil {
		return sitesForDepartment(sites, contentSites, departmentID)
	}
	return sitesForRegular(sites, contentSites)
}

func sitesForDepartment(sites []tenancyModels.Site, contentSites []uuid.UUID, departmentID *uuid.UUID) error {
	department, departmentOK := slicexx.FindFirst(sites, func(site tenancyModels.Site) bool {
		return site.ID == *departmentID
	})

	if !departmentOK {
		return errors.New("Department not found: " + departmentID.String())
	}
	if department.Type != tenancyModels.Department {
		return errors.New(fmt.Sprintf("Site is not a department: %v. ", department))
	}

	hosts := map[uuid.UUID]interface{}{}
	for _, host := range department.Hosts {
		hosts[host.ID] = struct{}{}
	}

	var badSites []uuid.UUID
	for _, siteID := range contentSites {
		if _, ok := hosts[siteID]; !ok {
			badSites = append(badSites, siteID)
		}
	}
	if len(badSites) > 0 {
		return errors.New(fmt.Sprintf("Site(s) are not in the department's hosts: %s. ", slicexx.JoinAsStrings(badSites, ", ")))
	}
	return nil
}
func sitesForRegular(sites []tenancyModels.Site, contentSites []uuid.UUID) error {
	var sitesNotFound []uuid.UUID
	var sitesAreDepartments []uuid.UUID
	for _, siteID := range contentSites {
		site, ok := slicexx.FindFirst(sites, func(site tenancyModels.Site) bool {
			return site.ID == siteID
		})

		if !ok {
			sitesNotFound = append(sitesNotFound, siteID)
		}
		if site.Type == tenancyModels.Department {
			sitesAreDepartments = append(sitesAreDepartments, siteID)
		}
	}

	err := ""
	if len(sitesNotFound) > 0 {
		err += fmt.Sprintf("Site(s) not found: %s. ", slicexx.JoinAsStrings(sitesNotFound, ", "))
	}
	if len(sitesAreDepartments) > 0 {
		err += fmt.Sprintf("Site(s) are departments: %s. ", slicexx.JoinAsStrings(sitesAreDepartments, ", "))
	}
	if len(err) > 0 {
		return errors.New(err)
	}

	return nil
}
