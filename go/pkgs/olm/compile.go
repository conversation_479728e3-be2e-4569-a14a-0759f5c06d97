package olm

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/helpers/ietags"
	"contentmanager/library/shared"
	hbsHelpers "contentmanager/library/templates/hbs_helpers"
	commonModels "contentmanager/library/tenant/common/models"
	publicModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/content"
	uuid "github.com/satori/go.uuid"
	"strings"
	"time"
)

type (
	ListViewModel struct {
		ID uuid.UUID
		content.Base
		commonModels.Tracking

		Name         string
		Description  string
		ContentTypes []string
		Items        []ietags.FragmentViewModel
		Content      string
		Errors       string
	}

	Params struct {
		IDOrName            string
		IgnoreSite          bool
		IgnorePublishPeriod bool
	}
)

func Compile(appContext *shared.AppContext, params Params) (*ListViewModel, error) {
	if appContext.CurrentSiteID() == uuid.Nil {
		return nil, auth.NewForbiddenError("Session is required to access a fragment. ")
	}
	if appContext.Account() == nil {
		return nil, auth.NewForbiddenError("Account is required to access a fragment. ")
	}

	now := appContext.AppTime().NowUTC()
	currentSiteID := appContext.CurrentSiteID()
	privacyLevel := appContext.PublicAccount().PrivacyLevel

	var list List
	dbQuery := appContext.TenantDatabase().Model(&list)

	if !params.IgnoreSite {
		dbQuery = dbQuery.Where(" ? = ANY(sites)", currentSiteID)
	}
	if !params.IgnorePublishPeriod {
		dbQuery = dbQuery.Where(pgxx.PublishPeriod(now, "", false))
	}

	if id, err := uuid.FromString(params.IDOrName); err != nil {
		dbQuery = dbQuery.Where("name = ?", params.IDOrName)
	} else {
		dbQuery = dbQuery.Where("id = ?", id)
	}

	if err := dbQuery.
		Where("active").
		Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0) ", privacyLevel). // TODO: move to pgxx
		Preload("Structure").
		First(&list).Error; err != nil {
		return nil, err
	}

	listView := ListViewModel{
		ID:           list.ID,
		Base:         list.Base,
		Tracking:     list.Tracking,
		Name:         list.Name,
		Description:  list.Description,
		ContentTypes: list.ContentTypes,
		Items:        []ietags.FragmentViewModel{},
		Content:      "",
	}
	if len(list.Items) == 0 {
		return &listView, nil
	}

	contentIDs := slicexx.Select(list.Items, func(item Item) uuid.UUID {
		return item.ContentID
	})
	var ff []publicModels.ContentForHandlebars
	dbFragmentQuery := appContext.TenantDatabase().Model(&ff)
	if !params.IgnoreSite {
		dbFragmentQuery = dbFragmentQuery.Where(" ? = ANY(sites)", currentSiteID)
	}
	if err := dbFragmentQuery.
		Where("active").
		Where(pgxx.PublishPeriod(now, "", false)).
		Where(pgxx.FieldInArray("id", contentIDs)).
		Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0) ", privacyLevel).
		Preload("ContentStructure").
		Preload("Media").
		//Preload("Tags").
		Find(&ff).Error; err != nil {
		return nil, err
	}
	if len(ff) == 0 {
		return &listView, nil
	}

	if mapped, err := publicModels.MapTagsForContents(appContext.TenantDatabase(), ff); err == nil {
		ff = mapped
	}

	fragmentsMap := slicexx.AsMap(ff, func(fragment publicModels.ContentForHandlebars) uuid.UUID {
		return fragment.ID
	})

	// Create fragment view models and apply overrides
	fragmentViewModels := make([]ietags.FragmentViewModel, 0, len(list.Items))
	mediaIDs := make([]uuid.UUID, 0, len(list.Items))
	for _, item := range list.Items {
		fragment, ok := fragmentsMap[item.ContentID]
		if !ok {
			continue
		}
		if fragment.Data == nil {
			fragment.Data = []byte("{}")
		}
		vm := ietags.FragmentViewModel{
			ContentForHandlebars: fragment,
			Timezone:             appContext.Timezone(),
			Account:              appContext.PublicAccount(),
			Site:                 appContext.CurrentSiteViewModel(),
			Dct:                  fragment.Data,
			DctMap:               utils.BytesToMapStringInterface(fragment.Data),
		}
		if len(item.Overrides) == 0 {
			fragmentViewModels = append(fragmentViewModels, vm)
			continue
		}

		vm = applyOverrides(vm, item.Overrides)

		// all available options are here: react/src/components/structure/SchemaSectionsSelector.tsx
		if title, ok := utils.GetValueFromJSONMap[string](item.Overrides, "ContentTitle"); ok {
			vm.Title = title
		}
		//if _, ok := GetValueFromJSONMap[string](item.Overrides, "ContentDescription"); ok {
		//	// TODO: vm.Description = description ???
		//}
		if expireAt, ok := utils.GetValueFromJSONMap[*time.Time](item.Overrides, "ContentExpireAt"); ok {
			vm.ExpireAt = expireAt
		}
		if mediaID, ok := utils.GetValueFromJSONMap[uuid.UUID](item.Overrides, "ContentMedia"); ok {
			vm.MediaID = uuid.NullUUID{UUID: mediaID, Valid: true}
			mediaIDs = append(mediaIDs, mediaID)
		}

		// revalidate fragment publish period (PrivacyLevel is already checked in queries and can't be overridden)
		if vm.ExpireAt != nil && vm.ExpireAt.Before(now) {
			continue
		}
		if vm.PublishAt == nil || vm.PublishAt.After(now) {
			continue
		}

		fragmentViewModels = append(fragmentViewModels, vm)
	}

	// preload overridden media
	if len(mediaIDs) > 0 {
		var media []commonModels.Media
		if err := appContext.TenantDatabase().Model(&media).
			Where("id in(?)", mediaIDs).
			Find(&media).Error; err != nil {
			return nil, err
		}
		mediaMap := slicexx.AsMap(media, func(m commonModels.Media) uuid.UUID {
			return m.ID
		})
		for i, vm := range fragmentViewModels {
			if !vm.MediaID.Valid {
				continue
			}
			if media, ok := mediaMap[vm.MediaID.UUID]; ok {
				fragmentViewModels[i].Media = media
			}
		}
	}

	listView.Items = fragmentViewModels

	// done with overrides and filtering, now compile fragments
	if len(list.Template) == 0 {
		list.Template = "{{ListItems}}"
	}

	helpers := hbsHelpers.NewHbsHelpers(appContext)
	if strings.Contains(list.Template, "{{ListItems}}") {
		fragmentsTemplate, err := handlebars.Parse(list.Structure.Template)
		if err != nil {
			return nil, err
		}
		var fragmentsHTML strings.Builder
		var ee strings.Builder
		for i, _ := range listView.Items {
			if err := ietags.BuildFragment(appContext, helpers, fragmentsTemplate, &listView.Items[i]); err != nil {
				ee.WriteString(err.Error())
				continue
			}
			fragmentsHTML.WriteString(listView.Items[i].Content)
		}
		list.Template = strings.ReplaceAll(list.Template, "{{ListItems}}", fragmentsHTML.String())
		listView.Errors = ee.String()
	}

	listTemplate, errLTPL := handlebars.Parse(list.Template)
	if errLTPL != nil {
		return nil, errLTPL
	}

	var err error
	listView.Content, err = listTemplate.ExecWithHelpers(listView, helpers)
	if err != nil {
		return nil, err
	}

	return &listView, nil
}

func applyOverrides(fragmentViewModel ietags.FragmentViewModel, overrides map[string]interface{}) ietags.FragmentViewModel {
	for key, value := range overrides {
		fragmentViewModel.DctMap[key] = value
	}
	if len(overrides) > 0 {
		fragmentViewModel.Dct = utils.MapStringInterfaceToBytes(fragmentViewModel.DctMap)
	}
	return fragmentViewModel
}
