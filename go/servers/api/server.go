package main

import (
	"contentmanager/etc/conf"
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/helpers/init_handlebars"
	"contentmanager/library/httpService"
	"contentmanager/library/httpService/controller/status"
	"contentmanager/library/shared"
	tenancyControllers "contentmanager/library/tenancy/controllers"
	adminControllers "contentmanager/library/tenant/admin/controllers"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/amzn"
	"contentmanager/pkgs/amzn/amzn_v2"
	"contentmanager/pkgs/auth/identity"
	identityHandlers "contentmanager/pkgs/auth/identity/handlers"
	login "contentmanager/pkgs/auth/login/routes"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	admin22 "contentmanager/pkgs/content/add_admin"
	admin3 "contentmanager/pkgs/content/fragments/admin"
	admin5 "contentmanager/pkgs/forms/admin"
	admin6 "contentmanager/pkgs/history"
	admin10 "contentmanager/pkgs/image_crop_size/admin"
	"contentmanager/pkgs/media"
	"contentmanager/pkgs/multitenancy"
	"contentmanager/pkgs/navigation"
	"contentmanager/pkgs/notifications/admin"
	admin2 "contentmanager/pkgs/olm/admin"
	admin11 "contentmanager/pkgs/queries/admin"
	admin12 "contentmanager/pkgs/reservation/admin"
	"contentmanager/pkgs/sauth"
	admin7 "contentmanager/pkgs/search_v2/admin"
	admin8 "contentmanager/pkgs/search_v2/promotions/admin"
	admin9 "contentmanager/pkgs/search_v2/suggestions/admin"
	"contentmanager/pkgs/settings"
	"contentmanager/pkgs/storage"
	structureHandlers "contentmanager/pkgs/structure/handlers"
	admintag "contentmanager/pkgs/tags"
	admin4 "contentmanager/pkgs/templates/admin"
	accountsHandlers "contentmanager/pkgs/user_management/accounts/handlers"
	groupsHandlers "contentmanager/pkgs/user_management/groups/handlers"
	"contentmanager/pkgs/user_management/pat/handlers"
	"contentmanager/tests/db_seed"
	"context"
	"flag"
	"fmt"
	_ "go.uber.org/automaxprocs"
	"net/http"
	"os"
	"os/signal"
	"reflect"
	"runtime"
	"strings"
	"time"
)

var serviceName = "API Server"

func init() {
	init_handlebars.InitHandlebars()
}

func wireUpRouter(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	// Dummy route for redirecting on login, to avoid 404 for development
	r.Get("/api/v2/version", func(rw http.ResponseWriter, r *shared.AppContext, c httpService.Context) {
		version := os.Getenv("APP_VERSION")
		if version == "" {
			version = "cm-api:dev"
		}
		rw.Write([]byte(version))
	})

	r.Get("/", func(rw http.ResponseWriter, r *shared.AppContext, c httpService.Context) {
		rw.Write([]byte("Welcome!"))
	})
	r.Get("/api/v1/dev/routes", middlewares.RequireAdminMiddleware(), func(rw http.ResponseWriter, c httpService.Context, route httpService.Routes) {
		res := strings.Builder{}
		for _, r := range route.All() {
			handler := ""
			t := ""
			if len(r.Handlers()) > 0 {
				lastHandler := r.Handlers()[len(r.Handlers())-1]
				handler = runtime.FuncForPC(reflect.ValueOf(lastHandler).Pointer()).Name()
				handler = strings.Replace(handler, "contentmanager/library/tenant/admin/", "", -1)
				handler = strings.Replace(handler, "contentmanager/library/tenant/common/", "", -1)
				handler = strings.Replace(handler, "contentmanager/library/tenant/admin/pkgs/", "", -1)
				handler = strings.Replace(handler, "contentmanager/library/", "", -1)
				handler = strings.Replace(handler, "pkgs/", "", -1)
				handlerTypeOf := reflect.TypeOf(lastHandler)
				t = handlerTypeOf.String()
			}
			res.WriteString(fmt.Sprintf("[%s]:\t%s\t%s\t(%s)\n", r.Method(), r.Pattern(), handler, t))
		}
		rw.Write([]byte(res.String()))
	})
	//Health Check
	r.Get(conf.RouterApiStatus, status.StatusController{}.CheckServerStatus)

	/*******************************************************************************************************************
	//Admin API Controllers
	*******************************************************************************************************************/
	//Azure OAuth AzureLoginHandler
	//r.Post(conf.RouterAuthorize, commonControllers.DirectoryLogin{}.Authorize)
	//r.Get(conf.RouterRedirectAdminConsent, commonControllers.DirectoryLogin{}.RedirectToAdminConsent)
	//r.Get(conf.RouterProcessAdminConsent, commonControllers.DirectoryLogin{}.ProcessAdminConsent)
	////Google OAuth AzureLoginHandler
	//r.Post(conf.RouterGoogleAuthorize, commonControllers.GoogleDirectoryLogin{}.Authorize)
	//Authentication Routes
	//r.Get(conf.RouterLogout, commonControllers.AuthenticationController{}.Logout)
	//r.Post(conf.RouterLogin, commonControllers.AuthenticationController{}.Authenticate)

	r.Group("", func(router httpService.Router) {
		//Content
		r.Delete(conf.RouterApiContent, deprecated)
		r.Delete(conf.RouterApiContent, deprecated)
		r.Put(conf.RouterApiContent, deprecated)
		r.Get(conf.RouterApiContentEditors, deprecated)
		r.Patch(conf.RouterApiRestoreContentById, deprecated)
		r.Get(conf.RouterApiContentPreviewById, deprecated)
		r.Post(conf.RouterApiCloneContentById, deprecated)

		r.Get(conf.RouterApiContent, adminControllers.ContentController{}.GetAdminContent)
		r.Get(conf.RouterApiContentById, adminControllers.ContentController{}.GetAdminContentById)
		r.Patch(conf.RouterApiContentById, adminControllers.ContentController{}.PatchContentById)
		// Deprecated
		r.Post(conf.RouterApiContent, adminControllers.ContentController{}.PostContent)

		// Template
		r.Get("/api/v1/template", adminControllers.GetTemplates)
		r.Get("/api/v1/template/list", adminControllers.PaginateTemplates)
		//Content/Preview

		//Media
		r.Get(conf.RouterApiMediaV2, adminMedia.AdminMedia_Get)
		r.Post(conf.RouterApiMediaV2, adminMedia.UploadImage_POST)
		r.Put(conf.RouterApiMediaV2, adminMedia.EditImageData_PUT)
		r.Delete(conf.RouterApiMediaV2+"/:id", adminMedia.Image_DELETE)

		r.Get(conf.RouterApiS3, adminControllers.S3Controller{}.GetAdminS3)
		r.Put(conf.RouterApiS3, adminControllers.S3Controller{}.UpdateAdminS3Media)
		//Site
		r.Get(conf.RouterApiSite, tenancyControllers.SiteController{}.GetAdminSite)

		r.Get("/api/v1/site/domains", tenancyControllers.SiteController{}.GetDomains)
		r.Get("/api/v1/site/instagram-errors", tenancyControllers.SiteController{}.GetInstagramErrors)
		r.Get("/api/v1/site/timezone", tenancyControllers.SiteController{}.GetTimezone)
		r.Get(conf.RouterApiSiteByID, tenancyControllers.SiteController{}.GetAdminSiteByID)
		r.Get("/api/v1/department", tenancyControllers.GetDepartments)
		r.Put("/api/v1/department", tenancyControllers.SiteController{}.CreateDepartment)

		r.Put(conf.RouterApiSite, tenancyControllers.SiteController{}.UpdateAdminSite)

		//Role
		r.Get(conf.RouterApiRole, middlewares.RequireAdminMiddleware(), adminControllers.RoleController{}.GetTenantRole)
		r.Put(conf.RouterApiRole, middlewares.RequireAdminMiddleware(), adminControllers.RoleController{}.PutTenantRole)
		r.Post(conf.RouterApiRole, middlewares.RequireAdminMiddleware(), adminControllers.RoleController{}.PostTenantRole)
		//Transportation Area
		r.Get(conf.RouterApiBusArea, adminControllers.BusAreaController{}.GetAdminBusArea)
		r.Post(conf.RouterApiBusArea, adminControllers.BusAreaController{}.PostAdminBusArea)
		r.Put(conf.RouterApiBusArea, adminControllers.BusAreaController{}.UpdateAdminBusArea)
		r.Delete(conf.RouterApiBusArea, adminControllers.BusAreaController{}.DeleteAdminBusArea)
		//Bus Route
		r.Get(conf.RouterApiBusRoute, adminControllers.BusRouteController{}.GetAdminBusRoute)
		r.Post(conf.RouterApiBusRoute, adminControllers.BusRouteController{}.PostAdminBusRoute)
		r.Put(conf.RouterApiBusRoute, adminControllers.BusRouteController{}.UpdateAdminBusRoute)
		r.Delete(conf.RouterApiBusRoute+"/:id", adminControllers.BusRouteController{}.DeleteAdminBusRoute)
		//Bus Status
		r.Get(conf.RouterApiBusStatus, adminControllers.BusStatusController{}.GetAdminBusStatus)
		r.Post(conf.RouterApiBusStatus, adminControllers.BusStatusController{}.PostAdminBusStatus)
		r.Put(conf.RouterApiBusStatusById, adminControllers.BusStatusController{}.PutAdminBusStatus)
		r.Delete(conf.RouterApiBusStatus, adminControllers.BusStatusController{}.DeleteAdminBusStatus)
		//Navigation
		r.Get(conf.RouterApiNavigation, navigation.NavigationController{}.GetAdminNavigation)
		r.Patch(conf.RouterApiNavigation, navigation.NavigationController{}.PatchAdminNavigation)
		r.Get(conf.RouterApiNavigationByContentID, navigation.NavigationController{}.GetAdminNavigationByContentID)
		r.Delete(conf.RouterApiNavigationByContentID, navigation.NavigationController{}.DeleteNavigationByContentId)
		//Document
		r.Get(conf.RouterApiDocument, adminControllers.DocumentController{}.GetAdminDocuments)
		r.Patch(conf.RouterApiDocument, adminControllers.DocumentController{}.UpdateAdminDocumentsLocation)
		r.Post(conf.RouterApiDocument, adminControllers.DocumentController{}.PostAdminDocument)
		r.Delete(conf.RouterApiDocument, adminControllers.DocumentController{}.DeleteAdminDocuments)
		//Document/id
		r.Get(conf.RouterApiDocumentByID, adminControllers.DocumentController{}.GetAdminDocumentById)
		r.Patch(conf.RouterApiDocumentByID, adminControllers.DocumentController{}.UpdateAdminDocument)
		r.Put(conf.RouterApiDocumentByID, adminControllers.DocumentController{}.UpdateDocumentBinaryById)
		//Document/id/ancestors
		r.Get(conf.RouterApiDocumentAncestorsByID, adminControllers.DocumentController{}.GetAdminDocumentAncestorsById)

		// Legacy URLs admin
		r.Get(conf.RouterApiLegacyUrlsByContentId, adminControllers.LegacyURLsController{}.GetLegacyURLsForContentID)
		r.Post(conf.RouterApiLegacyUrlsByContentId, adminControllers.LegacyURLsController{}.ReplaceLegacyURLsForContentID)

		/*******************************************************************************************************************
		//Generic API Controllers
		*******************************************************************************************************************/
		//Public Media
		//r.Get(conf.RouterImages, publicControllers.MediaController{}.GetTenantPublicMedia)
		//r.Get(conf.RouterThumbnails, publicControllers.MediaController{}.GetTenantPublicThumbnail)

		r.Get("/api/v1/sites-for-tenant", func(w http.ResponseWriter, r *shared.AppContext) {
			sites, err := r.Sites()
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			utils.WriteResultJSON(w, permissions.GetSitesForTenant(sites))
		})
		//r.Put(conf.RouterApiMediaV2+"/:id/crop/:cropName", middlewares.TenantSitesMiddleware(), adminMedia.ImageCrop_PUT, bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())

		r.Group(conf.RouterApiMediaV2, func(router httpService.Router) {
			router.Put("/:id/crop/:cropID", adminMedia.ImageCrop_PUT)
		}, bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())

		r.Group("/api/v1/settings", func(router httpService.Router) {
			router.Get("", settings.Settings_GET)
			router.Get("/:id", settings.SettingsById_GET)
			router.Post("", settings.Settings_POST)
			router.Put("", settings.SettingsUpsert_PUT)
			router.Put("/:id", settings.Settings_PUT)
			router.Delete("/:id", settings.Settings_DELETE)
		}, bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())

		r.Group("/api/v1/pat", func(router httpService.Router) {
			router.Get("/:account_id", handlers.PAT_GET)
			router.Post("", handlers.PAT_POST)
			router.Delete("/:token_id", handlers.PAT_DELETE)
		})

	}, middlewares.RequiresAuthenticationMiddleware())

	return r
}

func deprecated(w http.ResponseWriter, r *shared.AppContext) {
	http.Error(w, fmt.Sprintf("attempting to access deprecated route: [%s]", r.Request().URL.Path), http.StatusInternalServerError)
}

func main() {
	logging.ServiceName = "cm-api"
	log := logging.RootLogger()

	_ = os.Setenv("APP_NAME", "cm-api")

	tenancyServiceConfig := config.Init()
	tenancyServiceConfig.ServicePort = "10000"

	//cache.ICacheAdapter().CacheObject(conf.TenancySiteUrlCachekey, tenancyServiceConfig.AdminSiteUrl, false)
	//cache.ICacheAdapter().CacheObject(conf.TenancyConfigCacheKey, tenancyServiceConfig, false)

	db_seed.Seed() // it will start only if env CM_E2E is set to 1

	var wait time.Duration
	flag.DurationVar(&wait, "graceful-timeout", time.Second*15, "the duration for which the server gracefully wait for existing connections to finish - e.g. 15s or 1m")
	flag.Parse()

	// Instantiate a new router
	m := middlewares.Default()

	m = wireUpRouter(m)

	m.Map(m.Router)

	if tenancyServiceConfig.Debug {
		m.Use(httpService.CorsMiddleware("http://localhost:3000"))
	}

	// TODO => MT Confirm
	//m.Map(tenancyService.ISiteAdapter())

	factory := multitenancy.AccessorFactoryLive(context.Background(), *tenancyServiceConfig)
	m.Map(factory)

	if tokenManager, err := token.NewTokenManager[identity.PublicAccount](token.Config{
		CookieName:       tenancyServiceConfig.CookieName,
		CookieKey:        tenancyServiceConfig.CookieKey,
		TokenExpiration:  conf.SessionCookieExpiry,
		OperationTimeout: time.Second * 5,
	}); err != nil {
		log.Fatal().Err(err).Msg("Error creating token manager")
	} else {
		m.Map(tokenManager)
	}

	//Pre-Production / Test Environment
	m = admin.AddAdminNotifications(m)
	m = groupsHandlers.AddGroupsManagement(m)
	m = accountsHandlers.AddAccountsManagement(m)
	m = structureHandlers.AddStructure(m)
	m = identityHandlers.AddIdentity(m)
	m = admin2.AddOLM(m)
	m = admin2.AddDistributedLists(m)
	m = admin22.AddContentV2(m)
	m = admin3.AddFragments(m)
	m = admin4.AddTemplatesV2(m)
	m = admin6.AddHistory(m)
	m = admin5.AddAdminForms(m)
	m = admintag.AddTags(m)
	m = admin7.AddAdminSearch(m)
	m = storage.AddStorage(m)
	m = amzn_v2.AddAmzn2(m)
	m = admin8.AddPromotionsAdmin(m)
	m = admin9.AddSuggestionsAdmin(m)
	m = sauth.AddSAuth(m, sauth.MapSocAuthConfig(*tenancyServiceConfig))
	m = login.AddRoutes(m)
	m = admin10.AddImageCropSize(m)
	m = admin11.AddQueries(m)
	m = admin12.AddAdminReservations(m)

	amzn.StartRequestInvalidator(context.Background(), time.Minute*2)

	bindauth.ValidateRoutes(m.Router)
	srv := &http.Server{
		Addr: tenancyServiceConfig.ServiceHost + ":" + tenancyServiceConfig.ServicePort,
		// Good practice to set timeouts to avoid Slowloris attacks.
		WriteTimeout: time.Second * 90,
		ReadTimeout:  time.Second * 90,
		IdleTimeout:  time.Second * 90,
		Handler:      m, // Pass our instance of gorilla/mux in.
	}

	log.Info().Msgf("Starting up %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort)
	go func() {
		if err := srv.ListenAndServe(); err != nil {
			log.Error().Stack().Err(err).Msgf("Can't start %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort)
		}
	}()

	c := make(chan os.Signal, 1)
	// We'll accept graceful shutdowns when quit via SIGINT (Ctrl+C)
	// SIGKILL, SIGQUIT or SIGTERM (Ctrl+/) will not be caught.
	signal.Notify(c, os.Interrupt)

	// Block until we receive our signal.
	<-c

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), wait)
	defer cancel()
	// Doesn't block if no connections, but will otherwise wait
	// until the timeout deadline.
	srv.Shutdown(ctx)
	// Optionally, you could run srv.Shutdown in a goroutine and block on
	// <-ctx.Done() if your application should wait for other services
	// to finalize based on context cancellation.
	log.Info().Msgf("Shutting down %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort)
	os.Exit(0)

}
