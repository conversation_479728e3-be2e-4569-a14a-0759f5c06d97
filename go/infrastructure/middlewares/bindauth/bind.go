package bindauth

import (
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/library/utils/refxx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/permissions"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gorm.io/gorm/schema"
	"mime/multipart"
	"net/http"
	"reflect"
	"regexp"
	"strings"
	"sync"
)

type bindableParams interface{ BindParams() }
type BindableParams struct{}

func (BindableParams) BindParams() {}

var bindableType = reflect.TypeOf((*bindableParams)(nil)).Elem()
var appStateType = reflect.TypeOf((*shared.AppContext)(nil))
var tablerType = reflect.TypeOf((*schema.Tabler)(nil)).Elem()
var sharableType = reflect.TypeOf((*auth.Sharable)(nil)).Elem()

func BindParamsMiddleware() httpService.Handler {
	return func(w http.ResponseWriter, appCtx *shared.AppContext, p httpService.Params, c httpService.Context, routes httpService.Routes, router httpService.Route) {
		once.Do(func() {
			if err := validateRoutes(routes); err != nil {
				panic(err)
			}
		})

		handler := router.Handlers()[len(router.Handlers())-1]
		handlerType := reflect.TypeOf(handler)
		inType := handlerType.In(handlerType.NumIn() - 1)
		if !inType.Implements(bindableType) {
			c.Next()
			return
		}

		// Create a new instance of the params struct
		paramsVal := reflect.New(inType)
		// params := paramsVal.Interface()

		// Use reflection to iterate over the fields of the struct
		for i := 0; i < paramsVal.Elem().NumField(); i++ {
			field := paramsVal.Elem().Field(i)
			fieldType := field.Type()

			// Check the field name and call the respective function
			fieldName := paramsVal.Elem().Type().Field(i).Name
			switch fieldName {
			case "FromPath":
				val, err := fromPath(fieldType, p)
				if err != nil {
					utils.WriteResponseJSON(w, nil, errors.Wrap(err, "Error decoding from path"))
					return
				}

				if err := validateSites(appCtx, val); err != nil {
					utils.WriteResponseJSON(w, nil, err)
					return
				}

				field.Set(val)

			case "FromQuery":
				val, err := fromQuery(fieldType, appCtx.Request())
				if err != nil {
					utils.WriteResponseJSON(w, nil, errors.Wrap(err, "Error decoding from query"))
					return
				}

				if err := validateSites(appCtx, val); err != nil {
					utils.WriteResponseJSON(w, nil, err)
					return
				}

				field.Set(val)
			case "FromMultipartForm":
				val, err := fromMultipartForm(fieldType, appCtx.Request())
				if err != nil {
					utils.WriteResponseJSON(w, nil, errors.Wrap(err, "Error decoding from multipart form"))
					return
				}

				if err := validateSites(appCtx, val); err != nil {
					utils.WriteResponseJSON(w, nil, err)
					return
				}

				field.Set(val)

			case "FromBody":
				val, err := fromBody(fieldType, appCtx.Request())
				if err != nil {
					utils.WriteResponseJSON(w, nil, errors.Wrap(err, "Error decoding from body"))
					return
				}

				if err := validateSites(appCtx, val); err != nil {
					utils.WriteResponseJSON(w, nil, err)
					return
				}

				field.Set(val)
			case "FromForm":
				val, err := fromForm(fieldType, appCtx.Request())
				if err != nil {
					utils.WriteResponseJSON(w, nil, errors.Wrap(err, "Error decoding from form"))
					return
				}

				if err := validateSites(appCtx, val); err != nil {
					utils.WriteResponseJSON(w, nil, err)
					return
				}

				field.Set(val)
			}
		}

		// The same for DB. DB depends on other fields
		for i := 0; i < paramsVal.Elem().NumField(); i++ {
			fieldName := paramsVal.Elem().Type().Field(i).Name

			// Check the field name and call the respective function
			if !strings.HasPrefix(fieldName, "FromDB") {
				continue
			}

			if appCtx == nil || appCtx.TenantDatabase() == nil {
				utils.WriteResponseJSON(w, nil, errors.New("Error decoding from db: appCtx is nil"))
				return
			}

			query := paramsVal.Elem().Type().Field(i).Tag.Get("query")
			if query == "" {
				query = "id = @FromPath.ID"
			}
			params := extractKeys(query)
			for k := range params {
				if v, err := refxx.InterfaceByPath(paramsVal.Elem().Interface(), k); err == nil {
					params[k] = v
				} else {
					utils.WriteResponseJSON(w, nil, errors.Wrap(err, fmt.Sprintf("Error extracting value from path: %s from %v", k, paramsVal.Elem().Interface())))
					return
				}
			}

			fieldType := paramsVal.Elem().Field(i).Type()
			val, err := fromDB(fieldType, appCtx, query, params)
			if err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}
			field := paramsVal.Elem().Field(i)
			field.Set(val)
		}

		// Store the populated params struct in the context
		c.Set(inType, paramsVal.Elem())

		c.Next()
	}
}

var once sync.Once

func ValidateRoutes(routes httpService.Routes) {
	once.Do(func() {
		if err := validateRoutes(routes); err != nil {
			panic(err)
		}
	})
}

func validateRoutes(routes httpService.Routes) error {
	var ee []string
	for _, route := range routes.All() {
		handlers := route.Handlers()
		for i, handler := range handlers {
			handlerType := reflect.TypeOf(handler)
			for j := 0; j < handlerType.NumIn(); j++ {
				inType := handlerType.In(j)
				if !inType.Implements(bindableType) {
					continue
				}

				if i != len(handlers)-1 {
					ee = append(ee, fmt.Sprintf("Handler: %s, Route: %s - %s. BindableParams must be used only with the last handler of a Route. ", handlerType.String(), route.Method(), route.Pattern()))
				}
				if j != handlerType.NumIn()-1 {
					ee = append(ee, fmt.Sprintf("Handler: %s, Route: %s - %s BindableParams must be the last parameter in the handler function. ", handlerType.String(), route.Method(), route.Pattern()))
				}
				paramsVal := reflect.New(inType)
				for k := 0; k < paramsVal.Elem().NumField(); k++ {
					name := paramsVal.Elem().Type().Field(k).Name
					if name == "BindableParams" || name == "SkipAuth" {
						continue
					}

					if name == "FromPath" || name == "FromQuery" || name == "FromBody" || name == "FromMultipartForm" || name == "FromForm" {
						continue
					} else if strings.HasPrefix(name, "FromDB") {
						fieldType := paramsVal.Elem().Field(k).Type()
						if !fieldType.Implements(tablerType) {
							ee = append(ee, fmt.Sprintf("Handler: %s, Route: %s - %s. Field %s must implement schema.Tabler. ", handlerType.String(), route.Method(), route.Pattern(), name))
						}
					} else {
						ee = append(ee, fmt.Sprintf("Handler: %s, Route: %s - %s. BindableParams must contain only FromPath, FromQuery and FromBody. ", handlerType.String(), route.Method(), route.Pattern()))
					}
				}
			}
		}
	}

	if len(ee) > 0 {
		return fmt.Errorf("%v", strings.Join(ee, "; \n"))
	}
	return nil
}

func fromBody(t reflect.Type, r *http.Request) (reflect.Value, error) {
	val := reflect.New(t)

	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(val.Interface())
	if err != nil {
		return reflect.Value{}, err
	}

	return val.Elem(), nil
}

func fromQuery(t reflect.Type, r *http.Request) (reflect.Value, error) {
	val := reflect.New(t)

	if err := binding.MapFromMaps(val.Interface(), r.URL.Query()); err != nil {
		return reflect.Value{}, err
	}

	return val.Elem(), nil
}

func fromPath(t reflect.Type, p httpService.Params) (reflect.Value, error) {
	val := reflect.New(t)

	if err := binding.MapFromMaps(val.Interface(), p.AsMap()); err != nil {
		return reflect.Value{}, err
	}

	return val.Elem(), nil
}

func fromMultipartForm(t reflect.Type, r *http.Request) (reflect.Value, error) {
	val := reflect.New(t)

	if err := r.ParseMultipartForm(32 << 20); err != nil {
		return reflect.Value{}, err
	}

	if err := binding.MapFromMaps(val.Interface(), r.MultipartForm.Value); err != nil {
		return reflect.Value{}, err
	}

	elem := val.Elem()
	for i := 0; i < elem.NumField(); i++ {
		field := elem.Field(i)
		fieldType := field.Type()
		fieldName := t.Field(i).Name

		if fieldType == reflect.TypeOf((*multipart.FileHeader)(nil)) || fieldType == reflect.TypeOf([]*multipart.FileHeader{}) {
			files := r.MultipartForm.File[fieldName]
			if len(files) == 0 {
				continue
			}

			if fieldType == reflect.TypeOf((*multipart.FileHeader)(nil)) {
				field.Set(reflect.ValueOf(files[0]))
			} else {
				slice := reflect.MakeSlice(fieldType, len(files), len(files))
				for j, file := range files {
					slice.Index(j).Set(reflect.ValueOf(file))
				}
				field.Set(slice)
			}
		}
		switch {
		case fieldType == reflect.TypeOf((*multipart.FileHeader)(nil)):
			// Single file
			file, header, err := r.FormFile(fieldName)
			if err != nil {
				if errors.Is(err, http.ErrMissingFile) {
					continue // Skip if file is not provided
				}
				return reflect.Value{}, err
			}
			file.Close()
			field.Set(reflect.ValueOf(header))

		case fieldType == reflect.TypeOf([]*multipart.FileHeader{}):
			// Multiple files
			files := r.MultipartForm.File[fieldName]
			if len(files) == 0 {
				continue // Skip if no files are provided
			}
			slice := reflect.MakeSlice(fieldType, len(files), len(files))
			for j, file := range files {
				slice.Index(j).Set(reflect.ValueOf(file))
			}
			field.Set(slice)
		}
	}

	return val.Elem(), nil
}

func fromDB(t reflect.Type, r *shared.AppContext, query string, namedParams map[string]interface{}) (reflect.Value, error) {
	val := reflect.New(t)

	if err := r.TenantDatabase().Where(query, namedParams).First(val.Interface()).Error; err != nil {
		return reflect.Value{}, err
	}

	return val.Elem(), nil
}

func validateSites(appCtx *shared.AppContext, val reflect.Value) error {

	fn := func(val reflect.Value) error {
		if val.Type().Implements(sharableType) {
			sharable, ok := val.Interface().(auth.Sharable)
			if !ok || sharable == nil {
				return errors.New("validateSites: nil or invalid Sharable implementation")
			}
			return permissions.ValidateSitesForSharable(appCtx, sharable)
		}
		return nil
	}

	return refxx.IterateIfSliceReturnFirstNonZero(val, fn)
}

func extractKeys(input string) map[string]interface{} {
	pattern := `@([a-zA-Z0-9_.]+)`
	re := regexp.MustCompile(pattern)

	matches := re.FindAllStringSubmatch(input, -1)
	result := make(map[string]interface{})

	for _, match := range matches {
		if len(match) > 1 {
			// The actual key is in the second element of the match
			result[match[1]] = struct{}{}
		}
	}

	return result
}

func fromForm(t reflect.Type, r *http.Request) (reflect.Value, error) {
	val := reflect.New(t)

	if err := r.ParseForm(); err != nil {
		return reflect.Value{}, err
	}

	if err := binding.MapFromMaps(val.Interface(), r.Form); err != nil {
		return reflect.Value{}, err
	}

	return val.Elem(), nil
}
