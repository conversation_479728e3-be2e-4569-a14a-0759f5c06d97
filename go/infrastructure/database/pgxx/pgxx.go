package pgxx

import (
	"contentmanager/library/utils/slicexx"
	"fmt"
	"strconv"
	"strings"
	"time"
)

func ArrayHasAll[T any](field string, arr []T) string {
	return fmt.Sprintf("%s @> '{%s}'", field, escape(slicexx.JoinAsStrings(arr, ",")))
}
func ArrayHasAny[T any](field string, arr []T) string {
	return fmt.Sprintf("%s && '{%s}'", field, escape(slicexx.JoinAsStrings(arr, ",")))
}

func FieldInArray[T any](field string, arr []T) string {
	return fmt.Sprintf("%s  = ANY('{%s}')", field, escape(slicexx.JoinAsStrings(arr, ",")))
}

func FieldNotInArray[T any](field string, arr []T) string {
	return fmt.Sprintf("NOT(%s  = ANY('{%s}'))", field, escape(slicexx.JoinAsStrings(arr, ",")))
}

func LTreeChildren(field string, path string) string {
	if len(path) == 0 {
		// Root level
		return fmt.Sprintf("nlevel(%s) = 1", field)
	}
	result := "$path @> $field AND $field != $path AND nlevel($field) = nlevel($path) + 1"
	result = strings.Replace(result, "$field", field, -1)
	result = strings.Replace(result, "$path", "'"+escape(path)+"'", -1)
	return result
}

func ILike(s string) string {
	return `%` + strings.ReplaceAll(s, " ", "%") + `%`
}

const template = "$publish_at <= '$now' AND COALESCE($expire_at, TIMESTAMP 'infinity') >= '$now'"

func PublishPeriod(now time.Time, prefix string, inverted bool) string {
	query := strings.ReplaceAll(template, "$publish_at", prefix+"publish_at")
	query = strings.ReplaceAll(query, "$expire_at", prefix+"expire_at")
	query = strings.ReplaceAll(query, "$now", now.Format("2006-01-02 15:04:05.999999 -07:00"))

	if inverted {
		query = "NOT(" + query + ")"
	}
	return query
}

func TimeRange(startDate, endDate time.Time, prefix string) string {
	field := prefix + "settings"
	start := startDate.Format("2006-01-02T15:04:05.999999Z")
	end := endDate.Format("2006-01-02T15:04:05.999999Z")
	timeRangeQuery := fmt.Sprintf("((immutable_timestamptz(%s->>'startdate') <= '%s' AND immutable_timestamptz(%s->>'enddate') >= '%s') OR (%s->>'rrule' IS NOT NULL AND %s->>'rrule' != ''))",
		field, end, field, start, field, field) // end and start must be "reversed" to match overlapping ranges
	return timeRangeQuery
}

// Status returns a query to filter by status. All statuses defined here: react/src/components/content/BaseForm.tsx
// 'draft' | 'published' | 'scheduled' | 'expired'
func Status(now time.Time, prefix string, status string) string {
	query := ""
	switch strings.ToLower(status) {
	case "draft":
		query = fmt.Sprintf("$publish_at IS NULL")
	case "published":
		query = template
	case "scheduled":
		query = fmt.Sprintf("$publish_at IS NOT NULL AND $publish_at > '$now'")
	case "expired":
		query = fmt.Sprintf("$publish_at IS NOT NULL AND $expire_at IS NOT NULL AND $expire_at < '$now'")
	default:
		panic("unknown status: " + status)
	}
	query = strings.ReplaceAll(query, "$publish_at", prefix+"publish_at")
	query = strings.ReplaceAll(query, "$expire_at", prefix+"expire_at")
	query = strings.ReplaceAll(query, "$now", now.Format("2006-01-02 15:04:05.999999 -07:00"))

	return query
}

func escape(str string) string {
	v := []byte(str)
	result := make([]byte, 0, len(v))
	for _, b := range v {
		if b == '\\' {
			result = append(result, '\\', '\\')
		} else if b == '\'' {
			result = append(result, '\'', '\'')
		} else if b < 0x20 || b > 0x7e {
			result = append(result, []byte(fmt.Sprintf("\\%03o", b))...)
		} else {
			result = append(result, b)
		}
	}
	return string(result)
}

const privacyLevelTemplate = "(($privacyLevel & $value) = $privacyLevel OR $privacyLevel = 0)"

func PrivacyLevel(value int, prefix string) string {
	query := strings.ReplaceAll(privacyLevelTemplate, "$privacyLevel", prefix+"privacy_level")
	query = strings.ReplaceAll(query, "$value", strconv.Itoa(value))
	return query
}
