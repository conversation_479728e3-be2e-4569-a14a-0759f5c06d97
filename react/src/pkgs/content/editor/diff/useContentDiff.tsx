import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react'
import { Content } from '../../types'
import _ from 'lodash'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import { DiffViewer } from '@/pkgs/monaco/diff-viewer'
import { alpha, Box, Button, Checkbox, FormControlLabel, FormGroup, Grid, Stack, Typography } from '@mui/material'
import { useStructureDetailsQuery } from '@/pkgs/structure/queries'
import CMDialog from '@/common/components/CMDialog'
import { getImagePreviewUrl } from '@/pkgs/storage/getImagePreviewUrl'
import { colours } from '@/common/colours'
import { ImageDiff } from './ImageDiff'
import moment from 'moment'
import { _isEqual } from '@/helpers'
import { TagsDiff } from './TagsDiff'
import UndoIcon from '@mui/icons-material/Undo'
import { LoadingButton } from '@mui/lab'
import {
    isAllTrue,
    isFieldChecked,
    isSectionChecked,
    isSectionComponentChecked,
    setAllValues,
    updateField,
    updateSection,
    updateSectionComponent,
    useContentFieldsToRevert
} from './useContentFieldsToRevert'
import { getContentChanges } from './getContentChanges'
import { useAppContext } from '@/pkgs/auth/atoms'
import './inverted.css'
import { useAtom } from 'jotai'
import Alert from '@mui/material/Alert'
import { InfoOutlined } from '@mui/icons-material'

// modifyValue() is executed and return value is passed to component()
interface ContentDiffConfigForField {
    modifyValue?: (value) => any
    component?: (beforeValue, afterValue) => ReactNode
}

// Content fields EXCEPT "Data" / Structure fields. For structure fields
function useContentDiffConfig(): Partial<Record<keyof Content, ContentDiffConfigForField>> {
    const appContext = useAppContext()

    return {
        MediaID: {
            component: (beforeValue, afterValue) => (
                <ImageDiff beforeImageId={beforeValue || ''} afterImageId={afterValue || ''} />
            )
        },
        PrivacyLevel: {
            modifyValue: (value) => {
                if (value == 0) {
                    return 'public'
                } else if (value == 2) {
                    return 'staff'
                }

                return ''
            }
        },
        Data: {
            component: (beforeValue, afterValue) => undefined
        },
        PublishAt: {
            modifyValue: (value) => (!value ? '-' : moment(value).format('YYYY-MM-DD, h:mm:ss a'))
        },
        ExpireAt: {
            modifyValue: (value) => (!value ? '-' : moment(value).format('YYYY-MM-DD, h:mm:ss a'))
        },
        Tags: {
            component: (beforeValue, afterValue) => <TagsDiff beforeValue={beforeValue} afterValue={afterValue} />
        },
        Sites: {
            modifyValue: (value) => (!value ? '-' : value.map((siteId) => appContext.getSiteName(siteId)))
        }
    }
}

// dct component list
const componentTypeConfig: Record<string, ContentDiffConfigForField> = {
    checkbox: {},
    document: {},
    image: {
        component: (beforeValue, afterValue) => (
            <ImageDiff beforeImageId={beforeValue?.src || ''} afterImageId={afterValue?.src || ''} />
        )
    },
    select: {},
    textarea: {},
    text: {},
    email: {},
    date: {},
    ['rich-text']: {},
    ['contact-form-link']: {}
}

interface UseContentDiffProps<T extends Content> {
    serverValue: T | undefined
    value: T | undefined
    hasExternalChanges: boolean
    title?: ReactNode
    onRevert?: (newValue: T, callback?: () => void) => void
    onRevertIsLoading?: boolean
    disabled?: boolean
}

function ContentSectionRow({ columns }: { columns: [JSX.Element, JSX.Element] }) {
    const sizing = [1.5, 10.5]
    return (
        <Grid
            item
            container
            md={12}
            display='flex'
            justifyContent='space-between'
            sx={{
                paddingLeft: '16px',
                paddingBottom: '12px',
                paddingTop: '12px',
                borderBottom: `1px solid ${colours.topbar}`,
                '&:hover': {
                    backgroundColor: alpha(colours.topbar, 0.1)
                }
            }}
        >
            <Grid item container md={sizing[0]} justifyContent='flex-start'>
                {columns[0]}
            </Grid>
            <Grid item container md={sizing[1]}>
                {columns[1]}
            </Grid>
        </Grid>
    )
}

type ContentPartial = Partial<Record<keyof Content, any>> | null

// UI for content diff and revert view
// See: getContentChanges.ts, useContentFieldsToRevert.ts
export default function useContentDiff<T extends Content>({
    serverValue,
    value,
    hasExternalChanges,
    title,
    onRevert,
    onRevertIsLoading,
    disabled
}: UseContentDiffProps<T>) {
    const contentDiffConfig = useContentDiffConfig()

    const [changed, setChanged] = useState(false)
    const [contentDiffViewerIsOpen, setContentDiffViewerIsOpen] = useState(false)
    const { data: structure } = useStructureDetailsQuery(value?.StructureID || '')

    const refreshChangesThrottled = () => {
        const changed = hasExternalChanges || !!Object.values(contentChanges)?.length
        setChanged(changed)

        // Uncomment for debugging:
        // changed && console.log('-------------------- difference -----------------')
        // changed && console.log(differenceObject(stateWithoutUpdated, originalStateWithoutUpdated))
    }

    useEffect(() => {
        refreshChangesThrottled()
    }, [value, serverValue, hasExternalChanges])

    const contentChanges = useMemo(() => {
        return getContentChanges(serverValue, value, structure)
    }, [serverValue, value, structure])

    const hasChanges = !!Object.keys(contentChanges).length

    const { fieldsToRevert, setFieldsToRevert } = useContentFieldsToRevert(contentChanges)

    const onRevertHandler = useCallback(() => {
        if (!onRevert || !serverValue || !value || !fieldsToRevert || !Object.values(fieldsToRevert)?.length) return
        const newValue = { ...value }

        for (const key of Object.keys(contentChanges)) {
            if (key == 'Data') continue
            if (fieldsToRevert?.[key] === true && key in serverValue) {
                newValue[key] = serverValue[key]
            }
        }

        if ('Data' in fieldsToRevert) {
            for (const sectionName of Object.keys(fieldsToRevert['Data'])) {
                if (
                    typeof fieldsToRevert['Data'][sectionName] == 'boolean' &&
                    fieldsToRevert['Data'][sectionName] == true
                ) {
                    // this section allows multiple so until further development we revert the entire array of component values
                    newValue['Data'][sectionName] = serverValue['Data'][sectionName]
                } else {
                    for (const componentName of Object.keys(fieldsToRevert['Data'][sectionName])) {
                        if (fieldsToRevert['Data'][sectionName][componentName] == false) continue
                        // is an editor, force update after revert is applied

                        if (
                            !(sectionName in serverValue['Data']) ||
                            !(componentName in serverValue['Data']?.[sectionName])
                        ) {
                            delete newValue['Data'][sectionName]?.[componentName]
                        } else {
                            newValue['Data'][sectionName][componentName] =
                                serverValue['Data'][sectionName][componentName]
                        }
                    }
                }
            }
        }

        onRevert(newValue)
        setContentDiffViewerIsOpen(false)
    }, [serverValue, value, contentChanges, fieldsToRevert, onRevert])

    const renderContentDiffViewer = useCallback(() => {
        if (!contentDiffViewerIsOpen) return null
        return (
            <CMDialog
                open={contentDiffViewerIsOpen}
                showCloseButton
                onClose={() => setContentDiffViewerIsOpen(false)}
                fullWidth
                maxWidth={'xl'}
                title={
                    <Stack direction='column' width='100%'>
                        {disabled && (
                            <Alert severity={'info'} icon={<InfoOutlined sx={{ alignSelf: 'center' }} />}>
                                <Typography variant='subtitle1' sx={{ width: '100%' }}>
                                    An active Editing Session is required to utilize the Revert tool.
                                </Typography>
                            </Alert>
                        )}
                        <Typography padding='12px 16px' variant='h6'>
                            {!!!onRevert ? title : ''}
                        </Typography>
                        <ContentSectionRow
                            columns={[
                                !!onRevert ? <Typography variant='subtitle1'>Revert</Typography> : <></>,
                                <Stack key={11} direction='row' width={`calc(100% - 30px)`}>
                                    <Typography variant='subtitle1' sx={{ width: '100%' }}>
                                        Current
                                    </Typography>
                                    <Typography variant='subtitle1' sx={{ width: '100%' }}>
                                        {!!onRevert ? title : ''}
                                    </Typography>
                                </Stack>
                            ]}
                        />
                    </Stack>
                }
                sx={{
                    '& .MuiDialogTitle-root': {
                        padding: 0
                    }
                }}
            >
                <DialogContent sx={{ minHeight: '600px', padding: 0 }}>
                    {!hasChanges ? (
                        <Stack sx={{ justifyContent: 'center', alignItems: 'center', marginTop: '10%' }}>
                            <Typography>There are no changes.</Typography>
                            <Button
                                onClick={() => {
                                    setContentDiffViewerIsOpen(false)
                                }}
                            >
                                Close
                            </Button>
                        </Stack>
                    ) : null}
                    {value &&
                        Object.keys(contentChanges).map((key) => {
                            const isLong = ['Sites', 'Settings', 'Meta'].includes(key)

                            // Ignore all changes for form structure because they will be displayed rendered after
                            if (key == 'Data' || !contentChanges?.[key]) return undefined

                            let originalValue =
                                contentDiffConfig?.[key]?.modifyValue?.(contentChanges?.[key]?.originalValue) ||
                                contentChanges?.[key]?.originalValue
                            let modifiedValue =
                                contentDiffConfig?.[key]?.modifyValue?.(contentChanges?.[key]?.modifiedValue) ||
                                contentChanges?.[key]?.modifiedValue

                            if (isFieldChecked(fieldsToRevert, key)) {
                                modifiedValue = originalValue
                            }

                            return (
                                <ContentSectionRow
                                    key={key}
                                    columns={[
                                        <Stack key={12} direction='row' alignItems='center'>
                                            <FormGroup>
                                                <FormControlLabel
                                                    disabled={disabled}
                                                    control={
                                                        !!onRevert ? (
                                                            <Checkbox
                                                                checked={isFieldChecked(fieldsToRevert, key)}
                                                                onChange={(event) => {
                                                                    setFieldsToRevert(
                                                                        updateField(
                                                                            fieldsToRevert,
                                                                            key,
                                                                            event.target.checked
                                                                        )
                                                                    )
                                                                }}
                                                            />
                                                        ) : (
                                                            <Box marginLeft={'12px'}></Box>
                                                        )
                                                    }
                                                    label={
                                                        <Typography variant='subtitle1'>
                                                            {key || 'Unknown section'}:
                                                        </Typography>
                                                    }
                                                />
                                            </FormGroup>
                                        </Stack>,
                                        !!contentDiffConfig?.[key]?.component ? (
                                            contentDiffConfig?.[key]?.component(originalValue, modifiedValue)
                                        ) : (
                                            <DiffViewer
                                                key={1}
                                                original={originalValue}
                                                modified={modifiedValue}
                                                height={isLong ? 100 : 20}
                                            />
                                        )
                                    ]}
                                />
                            )
                        })}
                    {structure?.FormStructure?.map((section) => {
                        // if allowMultiple: show all multiples in diff viewer as json until further work
                        // otherwise display component as individual diffs
                        const sectionChanges = contentChanges?.['Data']?.[section.name]
                        const isAllowMultiple = !!sectionChanges?.originalValue || !!sectionChanges?.modifiedValue

                        const isChecked = isSectionChecked(fieldsToRevert, section, isAllowMultiple)

                        if (isAllowMultiple) {
                            return (
                                <ContentSectionRow
                                    key={`${section.name}-content-section-row`}
                                    columns={[
                                        <Stack key={13} direction='row' alignItems='center'>
                                            <FormGroup>
                                                <FormControlLabel
                                                    disabled={disabled}
                                                    control={
                                                        !!onRevert ? (
                                                            <Checkbox
                                                                checked={isChecked}
                                                                onChange={(event) => {
                                                                    setFieldsToRevert(
                                                                        updateSection(
                                                                            fieldsToRevert,
                                                                            section,
                                                                            event.target.checked,
                                                                            true
                                                                        )
                                                                    )
                                                                }}
                                                            />
                                                        ) : (
                                                            <Box marginLeft={'12px'}></Box>
                                                        )
                                                    }
                                                    label={
                                                        <Typography variant='subtitle1'>
                                                            {section?.title || section?.name || 'Unknown section'}:
                                                        </Typography>
                                                    }
                                                />
                                            </FormGroup>
                                        </Stack>,

                                        <DiffViewer
                                            key={`${section.name}-diff-viewer`}
                                            original={sectionChanges?.originalValue}
                                            modified={
                                                isChecked
                                                    ? sectionChanges?.originalValue
                                                    : sectionChanges?.modifiedValue
                                            }
                                            height={250}
                                        />
                                    ]}
                                />
                            )
                        }

                        if (!sectionChanges) return null

                        return (
                            <Stack key={`${section.name}-stack`} direction='column'>
                                <Stack direction='row' alignItems='center' paddingLeft={'16px'}>
                                    <FormGroup>
                                        <FormControlLabel
                                            disabled={disabled}
                                            control={
                                                !!onRevert ? (
                                                    <Checkbox
                                                        checked={isChecked}
                                                        onChange={(event) => {
                                                            setFieldsToRevert(
                                                                updateSection(
                                                                    fieldsToRevert,
                                                                    section,
                                                                    event.target.checked,
                                                                    false
                                                                )
                                                            )
                                                        }}
                                                    />
                                                ) : (
                                                    <Box marginLeft={'12px'}></Box>
                                                )
                                            }
                                            label={
                                                <Typography variant='subtitle1'>
                                                    {section?.title || section?.name || 'Unknown section'}:
                                                </Typography>
                                            }
                                        />
                                    </FormGroup>
                                </Stack>
                                ,
                                {section?.components?.map((component) => {
                                    const isRichText = component.type == 'rich-text' // lexical

                                    let originalValue = sectionChanges?.[component.name]?.originalValue
                                    let modifiedValue = sectionChanges?.[component.name]?.modifiedValue
                                    if (!originalValue && !modifiedValue) return undefined

                                    const isComponentChecked = isSectionComponentChecked(
                                        fieldsToRevert,
                                        section.name,
                                        component.name
                                    )

                                    if (isComponentChecked) {
                                        modifiedValue = originalValue
                                    }

                                    return (
                                        <ContentSectionRow
                                            key={`${section.name}-${component.name}`}
                                            columns={[
                                                <Stack key={14} direction='row' alignItems='center' marginLeft='1.2rem'>
                                                    <FormGroup>
                                                        <FormControlLabel
                                                            disabled={disabled}
                                                            control={
                                                                !!onRevert ? (
                                                                    <Checkbox
                                                                        checked={isSectionComponentChecked(
                                                                            fieldsToRevert,
                                                                            section.name,
                                                                            component.name
                                                                        )}
                                                                        onChange={(event) => {
                                                                            setFieldsToRevert(
                                                                                updateSectionComponent(
                                                                                    fieldsToRevert,
                                                                                    section.name,
                                                                                    component.name,
                                                                                    event.target.checked
                                                                                )
                                                                            )
                                                                        }}
                                                                    />
                                                                ) : (
                                                                    <Box marginLeft={'12px'}></Box>
                                                                )
                                                            }
                                                            label={
                                                                <Typography variant='caption' alignItems='center'>
                                                                    {component.label}:
                                                                </Typography>
                                                            }
                                                        />
                                                    </FormGroup>
                                                </Stack>,
                                                //@ts-ignore
                                                !!componentTypeConfig?.[component.type]?.component ? (
                                                    componentTypeConfig?.[component.type]?.component?.(
                                                        originalValue,
                                                        modifiedValue
                                                    )
                                                ) : (
                                                    <DiffViewer
                                                        language={isRichText ? 'html' : undefined}
                                                        original={originalValue}
                                                        modified={modifiedValue}
                                                        height={
                                                            isRichText
                                                                ? 250
                                                                : _.isObject(originalValue) || _.isObject(modifiedValue)
                                                                  ? 100
                                                                  : 20
                                                        }
                                                    />
                                                )
                                            ]}
                                        />
                                    )
                                })}
                            </Stack>
                        )
                    })}
                </DialogContent>
                {!!onRevert && hasChanges && (
                    <DialogActions sx={{ justifyContent: 'space-between', paddingX: '1.2rem' }}>
                        <Stack direction='row' alignItems='center'>
                            <FormGroup>
                                <FormControlLabel
                                    disabled={disabled}
                                    control={
                                        <Checkbox
                                            checked={isAllTrue(fieldsToRevert)}
                                            onChange={(event) => {
                                                setFieldsToRevert(
                                                    setAllValues(
                                                        JSON.parse(JSON.stringify(fieldsToRevert)),
                                                        isAllTrue(fieldsToRevert) ? false : true
                                                    )
                                                )
                                            }}
                                        />
                                    }
                                    label={<Typography variant='subtitle1'>Select All</Typography>}
                                />
                            </FormGroup>
                        </Stack>
                        <Stack direction='row' gap='25px'>
                            <Button
                                onClick={() => {
                                    setContentDiffViewerIsOpen(false)
                                }}
                            >
                                Close
                            </Button>
                            {!!onRevert && (
                                <LoadingButton
                                    variant='contained'
                                    startIcon={<UndoIcon />}
                                    onClick={() => onRevertHandler()}
                                    loading={onRevertIsLoading}
                                    disabled={disabled}
                                >
                                    revert
                                </LoadingButton>
                            )}
                        </Stack>
                    </DialogActions>
                )}
            </CMDialog>
        )
    }, [contentDiffViewerIsOpen, fieldsToRevert, contentChanges, disabled])

    return { changed, renderContentDiffViewer, setContentDiffViewerIsOpen }
}
