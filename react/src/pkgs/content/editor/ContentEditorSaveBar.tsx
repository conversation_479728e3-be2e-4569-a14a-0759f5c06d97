import { Content, ContentType, PublishPeriod } from '../types'
import React, { useEffect, useMemo, useRef } from 'react'
import { handlePreviewAtom } from '../../../common/components/selectors/SiteSelectorForContent'
import { atom, useAtom, useSetAtom } from 'jotai'
import { Card, Divider, FormControl, FormLabel, Stack } from '@mui/material'
import VisibilityIcon from '@mui/icons-material/Visibility'
import Button from '@mui/material/Button'
import PublishStatusLabel from './components/PublishStatusLabel'
import PublishOptionsRadio from './components/PublishOptionsRadio'
import { ColourHexValue, colourWithOpacity, colours } from '../../../common/colours'
import { useInputDisabler } from './imported-content/useInputDisabler'
import { useAppContext } from '../../auth/atoms'
import { bannerHeightAtom } from '@/app/AppTopBar/InstagramErrorsBanner'
import { appTopBarHeightAtom, drawerWidth } from '@/app/AppTopBar/AppTopBar'
import BackButton from '@/common/components/BackButton'
import { drawerIsOpenAtom } from '@/app/ApplicationWrapper'
import useContentDiff from './diff/useContentDiff'
import { primaryTheme } from '@/app/theme'
import { ReservationSwitch } from '@/pkgs/reservation/ReservationSwitch'
import { Reservable } from '@/pkgs/reservation/types'

function getPathToContentType(content: Content | any) {
    const contentType = content?.type || content?.Type

    if (contentType == ContentType.Page) {
        return '/pages'
    } else if (contentType == ContentType.News) {
        return '/news'
    } else if (contentType == ContentType.Event) {
        return '/events'
    }

    return ''
}

export type publishStatus = 'draft' | 'published' | 'scheduled' | 'expired'
export const publishStatusColour: Record<publishStatus, string> = {
    draft: colours.warning,
    published: colours.published,
    scheduled: colours.base_blue,
    expired: colours.expired
}

export function getPublishStatus(
    publishAt: string | Date | null | undefined,
    expireAt: string | Date | null | undefined
): publishStatus {
    if (!publishAt) return 'draft'

    const now = new Date()
    if (publishAt && new Date(publishAt) > now) return 'scheduled'
    if (expireAt && new Date(expireAt) < now) return 'expired'
    if (publishAt) return 'published'
    return 'draft'
}

export const changeStatusLabel: { [key in publishStatus]: { [key in publishStatus]: string } } = {
    draft: {
        draft: 'Save as Draft',
        published: 'Publish',
        scheduled: 'Schedule',
        expired: 'Expire'
    },
    published: {
        draft: 'Unpublish and Save as Draft',
        published: 'Update Publication',
        scheduled: 'Schedule',
        expired: 'Expire'
    },
    scheduled: {
        draft: 'Unschedule and Save as Draft',
        published: 'Publish Now',
        scheduled: 'Schedule',
        expired: 'Expire'
    },
    expired: {
        draft: 'Save as Draft',
        published: 'Publish',
        scheduled: 'Schedule',
        expired: 'Expire'
    }
}

interface ContentEditorSaveBarProps<T extends Content> {
    serverValue: T
    value: T
    hasExternalChanges: boolean

    onAction(action: 'save' | 'publish' | 'draft', state: T): void

    disabled?: boolean
    onChange?: (v: PublishPeriod) => void
    onChangeMode?: (checked: boolean) => void
}

export const containerEditorSaveBarHeightAtom = atom<number>(0)

export function ContentEditorSaveBar<T extends Content>({
    serverValue,
    value,
    hasExternalChanges,
    onAction,
    disabled,
    onChange,
    onChangeMode
}: ContentEditorSaveBarProps<T>) {
    const [bannerHeight] = useAtom(bannerHeightAtom)
    const [appTopBarHeight] = useAtom(appTopBarHeightAtom)
    const [drawerIsOpen] = useAtom(drawerIsOpenAtom)
    const barRef = useRef<HTMLDivElement | null>(null)
    const setHeight = useSetAtom(containerEditorSaveBarHeightAtom)

    const [handlePreview] = useAtom(handlePreviewAtom)

    const publishStatus = useMemo(() => getPublishStatus(value.PublishAt, value.ExpireAt), [value])

    const current = getPublishStatus(value.PublishAt, value.ExpireAt)
    const original = getPublishStatus(serverValue.PublishAt, serverValue.ExpireAt)
    const createLabel = () => {
        return changeStatusLabel[original][current]
    }
    const evaluators = useAppContext()
    const hasPermission = React.useMemo(() => evaluators.action(value, 'update'), [value])
    const { isInputDisabled, isFullyDisabled } = useInputDisabler({ content: value, hasPermission })

    const { changed } = useContentDiff({
        serverValue: serverValue,
        value: value,
        hasExternalChanges: hasExternalChanges
    })

    useEffect(() => {
        if (!barRef?.current) {
            setHeight(0)
        } else {
            const height = barRef?.current.getBoundingClientRect().height
            setHeight(height)
        }
    }, [barRef?.current])

    return (
        <>
            <div
                ref={barRef}
                style={{
                    position: 'fixed',
                    top: `${bannerHeight + appTopBarHeight}px`,
                    width: `calc(${'100%'} - ${drawerIsOpen ? drawerWidth : 0}px)`,
                    zIndex: 1001,
                    marginLeft: '-24px', // ApplicationWrapper padding
                    marginRight: '-24px',
                    transition: 'ease 0.5s',
                    borderBottom: changed ? `4px solid ${primaryTheme.palette.warning.main}` : undefined
                }}
            >
                <Card
                    style={{
                        paddingLeft: '3em',
                        paddingRight: '3em',
                        paddingTop: '0.25em',
                        paddingBottom: '0.25em',
                        overflow: 'visible',
                        justifyContent: 'space-between',
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '16px',
                        alignItems: 'center'
                    }}
                >
                    <BackButton route={getPathToContentType(serverValue)} />

                    <ReservationSwitch ID={value.ID} table={'content'} onChange={onChangeMode} disabled={disabled} />

                    <FormControl
                        variant='standard'
                        component='fieldset'
                        // style={{ width: '100%' }}
                        sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end' }}
                    >
                        <Stack direction='column'>
                            <FormLabel component='legend' sx={{ display: 'flex', alignItems: 'center' }}>
                                Status:&nbsp;
                                <PublishStatusLabel publishAt={value.PublishAt} expireAt={value.ExpireAt} />
                            </FormLabel>
                            <FormLabel component='legend'></FormLabel>

                            <PublishOptionsRadio
                                value={value}
                                onChange={(v) => {
                                    onChange?.(v)
                                }}
                                disabled={Boolean(isInputDisabled('published') || disabled)}
                            />
                        </Stack>
                        <div className='flex-row-align-center'>
                            <Button
                                variant='contained'
                                sx={{
                                    backgroundColor: colourWithOpacity(
                                        publishStatusColour[publishStatus] as ColourHexValue,
                                        0.9
                                    ),
                                    '&:hover': {
                                        backgroundColor: publishStatusColour[publishStatus]
                                    },
                                    minWidth: '260px'
                                }}
                                style={{
                                    marginTop: '5px',
                                    marginBottom: '5px',
                                    marginRight: '5px',
                                    width: '100%'
                                }}
                                onClick={() => onAction('save', value)}
                                disabled={Boolean(disabled) || isFullyDisabled}
                            >
                                {createLabel()}
                            </Button>
                        </div>
                        <Divider orientation='vertical' variant='middle' flexItem sx={{ marginX: '8px' }} />
                        {/* <Button
                            onClick={() =>
                                handlePreview({
                                    id: value.ID,
                                    type: value.Type,
                                    route: value.Route,
                                    sites: value.Sites
                                })
                            }
                        >
                            View Page
                        </Button> */}
                        <VisibilityIcon
                            style={{
                                color: colourWithOpacity(publishStatusColour[current] as ColourHexValue, 0.85),
                                fontSize: '2em',
                                cursor: 'pointer',
                                paddingLeft: '0.7em',
                                paddingRight: '0.7em'
                            }}
                            sx={{
                                '&:hover': {
                                    backgroundColor: colourWithOpacity(
                                        publishStatusColour[current] as ColourHexValue,
                                        0.15
                                    ),
                                    color: publishStatusColour[current],
                                    borderRadius: '8px'
                                }
                            }}
                            onClick={() =>
                                handlePreview({
                                    id: value.ID,
                                    type: value.Type,
                                    route: value.Route,
                                    sites: value.Sites
                                })
                            }
                        />
                    </FormControl>
                </Card>
            </div>
        </>
    )
}
