import { useAppNavigation } from '../../../app/useAppNavigation'
import React, { useEffect, useState } from 'react'
import { Content, ContentType } from '../types'
import { useStructuredContentValidators } from '../editor/useStructuredContentValidators'
import {
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack
} from '@mui/material'
import Button from '@mui/material/Button'
import { httpPost } from '@/common/client'
import { z } from 'zod'
import { useMutation } from '@tanstack/react-query'
import { BASE } from '@/common/constants'
import { useDefaultSitesForSelector } from '@/pkgs/auth/atoms'
import { RouteEditor } from '@/pkgs/content/editor/components/RouteEditor'

interface CreateResourceProps {
    open: boolean
    onClose: () => void
}

export const CreateResource = ({ open, onClose }: CreateResourceProps) => {
    const defaultSites = useDefaultSitesForSelector()
    const now = new Date().toISOString()

    const { navigateTo } = useAppNavigation()

    const create = useMutation([], (content: Content) => httpPost(`${BASE}/api/v2/content`, content, z.string()))

    useEffect(() => {
        if (!create.data) return

        navigateTo(`/resource/${create.data}`)
        onClose()
    }, [create.data])

    const [state, setState] = useState<Content>({
        Type: 'js',
        PageLayout: 'HTML',
        Title: '',
        Route: '',
        Sites: defaultSites || [],
        DepartmentID: null,
        Path: '',
        StructureID: null,
        ExpireAt: null,
        PublishAt: now as any,
        // To satisfy the Content read interface
        Meta: {},
        Data: {},
        ID: '',
        Created: now as any,
        Updated: now as any,
        Active: true,
        Deleted: null,
        MediaID: null,
        Owner: '',
        PrivacyLevel: 0,
        Publisher: '',
        Tags: [],
        Settings: {}
    })

    const { validateAll, errors } = useStructuredContentValidators(['Sites', 'Title'], state)

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth={'xl'}>
            <DialogTitle>Create {state.Type}</DialogTitle>
            <DialogContent>
                <FormControl fullWidth sx={{ my: 2 }}>
                    <InputLabel id={'demo-simple-select-label'}>Type</InputLabel>
                    <Select
                        variant={'outlined'}
                        labelId='demo-simple-select-label'
                        label={'Type'}
                        value={state.Type || 'js'}
                        error={!!errors.Type}
                        onChange={(event) => {
                            setState((prev) => ({ ...prev, Type: event.target.value }))
                        }}
                    >
                        <MenuItem value='js'>Javascript</MenuItem>
                        <MenuItem value='css'>CSS</MenuItem>
                    </Select>
                </FormControl>
                <RouteEditor
                    value={state.Title}
                    onChange={(v) => setState({ ...state, Title: v, Route: v })}
                    contentType={state.Type === 'js' ? ContentType.JS : ContentType.CSS}
                />
            </DialogContent>
            <DialogActions>
                <Stack direction={'row'} spacing={2}>
                    <Button variant='outlined' color='secondary' onClick={onClose}>
                        Cancel
                    </Button>

                    <Button
                        variant='contained'
                        color='primary'
                        disabled={create.isLoading}
                        onClick={() => {
                            if (!validateAll()) {
                                return
                            }
                            create.mutate(state)
                        }}
                    >
                        Create {state.Type}
                    </Button>
                </Stack>
            </DialogActions>
        </Dialog>
    )
}
