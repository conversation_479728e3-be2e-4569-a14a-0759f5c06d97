import React from 'react'
import { Al<PERSON>, Button, FormControl, FormHelperText, Stack } from '@mui/material'
import { notify } from '../../../helpers'
import { BackButton } from '../../../common/components'
import 'react-toastify/dist/ReactToastify.css'
import { useAppContext } from '../../auth/atoms'
import { CodeEditor } from '../../monaco/code-editor'
import { mustString } from '@/helpers/parseJSON'
import { BASE } from '@/common/constants'
import { typeToEntityScope } from '../../auth/entityScope'
import { useDisable } from '@/common/useDisable'
import { useParams } from 'react-router-dom'
import { useContentQueries } from '@/pkgs/content/queries'
import { Content, ContentType } from '@/pkgs/content/types'
import { useStructuredContentValidators } from '@/pkgs/content/editor/useStructuredContentValidators'
import { httpPut } from '@/common/client'
import { z } from 'zod'
import { RouteEditor } from '@/pkgs/content/editor/components/RouteEditor'
import { Box } from '@mui/system'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import PageContainerWithHeader from '@/common/components/PageContainerWithHeader'
import { SitesSelectorComponent } from '@/common/components/selectors/SitesSelectorComponent'

export default function ResourceEditor(props) {
    const { id } = useParams()
    const [state, setState] = React.useState<Content | undefined>(undefined)
    const { validateAll, errors, setErrors } = useStructuredContentValidators(
        ['Sites', 'Title', 'Path', 'Content'],
        state
    )

    const { fetcher, updater } = useContentQueries(id || '')
    const evaluators = useAppContext()
    const hasPermission = React.useMemo(() => evaluators.action(state, 'update'), [state])

    const [isLoading, setIsLoading] = React.useState<boolean>(false)

    useDisable()

    React.useEffect(() => {
        if (!fetcher.data) return
        if (state) return

        setState(fetcher.data)
    }, [fetcher.data])

    React.useEffect(() => {
        if (!state) {
            return
        }
        document.title = `${state?.Title ? `${String(state.Title)}-` : 'Resource Editor'}`
    }, [state])

    async function update() {
        setIsLoading(true)
        try {
            await httpPut(`${BASE}/api/v2/content/${id}`, state, z.string())
            notify('Successfully updated', 'info')
        } catch (error) {
            console.error(error)
            notify(<pre>{guessErrorMessage(error)}</pre>, 'error')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <PageContainerWithHeader
            title={'Resource Editor'}
            topRightElement={<BackButton label={'Back'} route={'/system/resources'} />}
        >
            {fetcher.isLoading && <div>Loading...</div>}
            {fetcher.isError && <Alert severity={'error'}>Error: {fetcher.error.message}</Alert>}
            {state && (
                <div>
                    {!hasPermission && (
                        <Alert severity={'info'}>
                            You do not have access to this resource. This is a read-only view.
                        </Alert>
                    )}
                    <Stack spacing={2} direction={'row'} width='100%'>
                        <FormControl sx={{ width: '50%' }}>
                            <RouteEditor
                                value={state.Route}
                                onChange={(v) => {
                                    // @ts-ignore
                                    setState((p) => ({ ...p, Route: v, Title: v }))
                                }}
                                contentType={state.Type === 'js' ? ContentType.JS : ContentType.CSS}
                            />
                            <FormHelperText>
                                Changing the route of a resource will break any existing imports.
                            </FormHelperText>
                        </FormControl>
                        <FormControl fullWidth sx={{ width: '50%' }}>
                            <SitesSelectorComponent
                                value={state}
                                onChange={(v) =>
                                    setState({
                                        ...state,
                                        Sites: v.Sites,
                                        DepartmentID: v.DepartmentID
                                    })
                                }
                                contentType={typeToEntityScope(state.Type)}
                                error={errors.Sites}
                            />
                        </FormControl>
                    </Stack>

                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row' }}>
                            <div
                                style={{
                                    backgroundColor: '#1e1e1e',
                                    border: '1px solid black',
                                    borderRadius: '5px 5px 0 0',
                                    color: 'white'
                                }}
                            >
                                <pre style={{ padding: '0 16px 5px 16px' }}>{state.Title}</pre>
                            </div>
                        </Box>
                        <Box sx={{ padding: '4px 0px 4px 4px' }}>
                            <Button
                                variant='contained'
                                color='primary'
                                disabled={!hasPermission || isLoading}
                                onClick={() => {
                                    if (!validateAll()) {
                                        return
                                    }
                                    update()
                                }}
                            >
                                Save
                            </Button>
                        </Box>
                    </Box>

                    <CodeEditor
                        value={mustString(state?.Content)}
                        height={'86vh'}
                        language={state.Type === 'js' ? 'javascript' : 'css'}
                        disabled={!hasPermission}
                        onChange={(value) => {
                            //@ts-ignore
                            state && setState((p) => ({ ...p, Content: value || '' }))
                        }}
                    />
                </div>
            )}
        </PageContainerWithHeader>
    )
}
