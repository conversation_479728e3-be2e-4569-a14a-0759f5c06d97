import { useCurrentSite, useTenantSites } from '@/pkgs/auth/atoms'
import { Autocomplete, TextField } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'

interface DepartmentsAutocompleteV2Props {
    value: string[]
    onChange: (v: string[]) => void
}

export function DepartmentsAutocompleteV2({ value, onChange }: DepartmentsAutocompleteV2Props) {
    const currentSite = useCurrentSite()
    const tenantSites = useTenantSites()

    const departments = useMemo(() => {
        if (!currentSite) return []

        return tenantSites.reduce((acc: { label: string; id: string }[], iter) => {
            if (iter.Type === 'department' && Array.isArray(iter.Hosts) && iter.Hosts.includes(currentSite?.ID)) {
                acc.push({ label: iter.Name, id: iter.ID })
            }
            return acc
        }, [])
    }, [currentSite, tenantSites])

    const departmentIdToDepartment = departments.reduce(
        (a, t) => ({
            ...a,
            [t.id]: t
        }),
        {} as Record<string, { id: string; label: string }>
    )

    return (
        <Autocomplete
            style={{ flex: '1' }}
            disabled={!currentSite || currentSite.Type === 'department'}
            onChange={(ev, newVal) => {
                onChange(newVal.map((v) => v.id))
            }}
            multiple
            filterSelectedOptions
            options={departments}
            isOptionEqualToValue={(o, v) => o.id === v.id}
            getOptionLabel={(option) => option.label}
            value={value.map((id) => departmentIdToDepartment[id])}
            renderInput={(params) => (
                <TextField
                    {...params}
                    variant='outlined'
                    label='Departments'
                    placeholder='Departments'
                    helperText='Search for content based on its department'
                />
            )}
        />
    )
}
