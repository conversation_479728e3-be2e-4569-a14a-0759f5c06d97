import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { isEqual } from 'lodash'
import { Autocomplete, TextField } from '@mui/material'
import { storage } from '../../../common/storage.service'
import { EditorsAPI } from '../../../common/constants'
import { useAppContext } from '@/pkgs/auth/atoms'
import { useQuery } from '@tanstack/react-query'
import { baseQueryConfig } from '@/common/react-query'
import { httpGet } from '@/common/client'
import { z } from 'zod'

const editor = z.object({
    id: z.string(),
    name: z.string()
})

export type Editor = z.infer<typeof editor>

interface EditorsSelectorsV2Props {
    value: string[]
    onChange: (v: string[]) => void
}

export function EditorsSelectorV2({ value, onChange }: EditorsSelectorsV2Props) {
    const results = useQuery({
        ...baseQueryConfig,
        queryKey: ['editors-query-key'],
        queryFn: async () => httpGet(EditorsAPI, null, z.any())
    })

    const editorIdToEditor = results?.data?.reduce(
        (a, t) => ({
            ...a,
            [t.id]: t
        }),
        {} as Record<string, Editor>
    )

    return (
        <Autocomplete
            style={{ flex: '1' }}
            onChange={(ev, newValue) => {
                onChange(newValue.map((e) => e.id))
            }}
            multiple
            filterSelectedOptions
            options={results?.data || []}
            isOptionEqualToValue={(o, v) => o.id === v.id}
            getOptionLabel={(option) => option.name}
            value={value.map((id) => editorIdToEditor[id])}
            renderInput={(params) => (
                <TextField
                    {...params}
                    variant='outlined'
                    label='Editors'
                    placeholder='Editors'
                    helperText='Search for content based on owner or last editor'
                />
            )}
        />
    )
}
