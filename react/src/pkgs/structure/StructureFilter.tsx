import { Alert, Autocomplete, Checkbox, FormControl, SelectProps, TextField, Typography } from '@mui/material'
import { Structure } from './types'
import { getStructureIdToStructure, useStructuresQuery } from './queries'
import { useMemo } from 'react'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

type StructureFilterProps = {
    value: string[] // structureIDs
    onChange: (structures: string[]) => void
    disabled?: boolean
    label?: string
    variant?: SelectProps['variant']
    required?: boolean
    error?: string
    allowedStructures?: string[] | null
    onlyContactForms?: boolean
}

export function StructureFilter({
    value, // StructureIDs
    onChange,
    disabled = false,
    label = 'Structure',
    variant = 'outlined',
    required = false,
    error,
    allowedStructures = [],
    onlyContactForms = false
}: StructureFilterProps) {
    const structureQuery = useStructuresQuery({ page: 1, pageSize: 1000 })

    // Filter structures based on allowedStructures/onlyContactForms
    const filteredStructures = useMemo(() => {
        if (!structureQuery?.data) return []

        let available = structureQuery.data.Rows

        if (allowedStructures?.length) {
            available = structureQuery.data?.Rows.filter((s) => allowedStructures.includes(s.ID))
        }

        if (onlyContactForms) {
            available = available.filter((s) =>
                s.FormStructure.some((fs) => fs.name === 'contact' && fs.components.some((c) => c.name === 'to'))
            )
        }
        return available
    }, [structureQuery, allowedStructures])

    const structureIdToStructure = getStructureIdToStructure(filteredStructures)

    if (structureQuery.isLoading) {
        return <CenteredSpinner />
    }

    if (structureQuery.isError) {
        return <Alert severity='error'>Error loading structures: {guessErrorMessage(structureQuery.error)}</Alert>
    }

    if (filteredStructures.length === 0) {
        return <Alert severity='warning'>No structures were found. </Alert>
    }

    return (
        <FormControl variant={variant || 'outlined'} required={required} sx={{ width: '100%' }} disabled={disabled}>
            <Autocomplete
                disabled={disabled}
                sx={{
                    flex: 1
                }}
                aria-required={required}
                multiple
                disableCloseOnSelect
                disablePortal
                options={filteredStructures}
                value={value.map((id) => structureIdToStructure[id])}
                onChange={(ev, newValue) => {
                    onChange(newValue.map((r) => r.ID))
                }}
                getOptionLabel={(option) => {
                    return option?.Name
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label || 'Type'}
                        variant={variant || 'outlined'}
                        required={required}
                    />
                )}
                renderOption={(props, option, { selected }) => {
                    const { ...optionProps } = props
                    return (
                        <li key={option.ID} {...optionProps}>
                            <Checkbox style={{ marginRight: 8 }} checked={selected} size='small' />
                            <Typography textTransform='capitalize'>{option?.Name}</Typography>
                        </li>
                    )
                }}
            />
        </FormControl>
    )
}
