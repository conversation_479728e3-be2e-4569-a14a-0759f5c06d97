import { colours } from '@/common/colours'
import { alpha, Checkbox, CheckboxProps, FormControlLabel, FormControlLabelProps, Typography } from '@mui/material'
import { ReactNode } from 'react'

interface CMCheckbox extends CheckboxProps {
    formControlLabelProps?: Omit<FormControlLabelProps, 'control' | 'label'>
    label: ReactNode
}
export function CMCheckbox({ formControlLabelProps, label, ...checkboxProps }: CMCheckbox) {
    const disabled = checkboxProps.disabled
    const checked = checkboxProps.checked
    let sx = {
        ...formControlLabelProps?.sx,
        padding: 0,
        margin: 0,

        border: `2px solid ${
            checked ? (disabled ? colours.disabled : colours.base_blue) : colours.off_white_but_darker
        }`,
        borderRadius: '8px',
        paddingRight: '8px',
        '&:hover': {
            backgroundColor: alpha(disabled ? colours.disabled2 : colours.base_blue, 0.3),
            boxShadow: `0px 1px 3px ${disabled ? colours.disabled2 : colours.base_blue}`,
            fontWeight: 600
        }
    }

    return (
        <FormControlLabel sx={sx} label={label} control={<Checkbox {...checkboxProps} />} {...formControlLabelProps} />
    )
}
