import { httpDelete, httpPut } from '@/common/client'
import { reservable } from '@/pkgs/reservation/types'

export const ReservableAPI = '/api/v1/reservable'
export const ReservableLockAPI = `${ReservableAPI}/lock`
export const ReservableLockOverrideAPI = `${ReservableLockAPI}/override`

interface reservationParams {
    ID: string
    Table: string
    NextEditingSession: string
    EditingSession: string | null
    ExtendedLock: string | null
}

export async function updateEditingSession(params: reservationParams) {
    return await httpPut(ReservableAPI, params, reservable)
}

export async function endEditingSession(id: string, table: string): Promise<void> {
    return httpDelete(`${ReservableAPI}?table=${table}&id=${id}`)
}

export async function endExtendedLock(id: string, table: string) {
    return httpDelete(`${ReservableLockAPI}?table=${table}&id=${id}`, reservable)
}

export async function overrideExtendedLock(id: string, table: string) {
    return httpDelete(`${ReservableLockOverrideAPI}?table=${table}&id=${id}`, reservable)
}

export function isReservableAPIPath(url: string | undefined, entityID: string): boolean {
    if (!url) {
        return false
    }
    try {
        const U = new URL(url, window.location.origin)
        return U.pathname.includes(ReservableAPI) && U.searchParams.get('id') === entityID
    } catch {
        return false
    }
}
