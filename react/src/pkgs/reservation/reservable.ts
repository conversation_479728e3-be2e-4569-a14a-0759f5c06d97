import { isFutureTimestamp, utc } from '@/pkgs/reservation/time'
import { NullableDate, Reservable } from '@/pkgs/reservation/types'

// isReservable should be kept in-sync with Go function (res *Reservable) IsReservable
export function isEditingSessionForUserOrAvailable(res: Reservable, key: string | null, userID: string | undefined) {
    return !isSessionLocked(res, key) && (!isExtendedLocked(res) || res.CurrentEditor == userID)
}

// isSessionLocked - potentially rename this function.
// It seems unintuitive that `isSessionLocked` will return false if it's locked, but the current user has the right Key
// It feels more like 'isSessionLockedByMe', but can't think of anything that sounds *right*.
export function isSessionLocked(res: Reservable | null, key: string | null) {
    if (res == null) {
        return false
    }
    if (res.EditingSession == null || !isFutureTimestamp(res.EditingSession)) {
        return false
    }
    return !isMatchingKey(res.EditingSession, key)
}

export function isUserTheCurrentEditorForSession(res: Reservable, key: string | null, userID?: string) {
    if (res.EditingSession == null) {
        return false
    }
    return isMatchingKey(key, res.EditingSession) && res.CurrentEditor == userID
}

export function isExtendedLocked(res: Reservable | null) {
    if (res == null || res.ExtendedLock == null) {
        return false
    }
    return isFutureTimestamp(res.ExtendedLock)
}

export function isMatchingKey(resKey1: NullableDate, resKey2: NullableDate) {
    // String | Null can be directly compared
    if (resKey1 === resKey2) {
        return true
    }
    return utc(resKey1).isSame(utc(resKey2))
}

export function asReservable(entity: Reservable) {
    return {
        ID: entity?.ID,
        EditingSession: entity?.EditingSession,
        CurrentEditor: entity?.CurrentEditor,
        ExtendedLock: entity?.ExtendedLock
    }
}
