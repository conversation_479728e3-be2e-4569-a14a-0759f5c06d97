import { Alert, Autocomplete, Checkbox, FormControl, SelectProps, TextField, Typography } from '@mui/material'
import { useMemo } from 'react'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useTemplatesQuery } from '@/pkgs/content/editor/queries'
import { Content } from '@/pkgs/content/types'

type TemplateFilterProps = {
    value: string[] // template IDs
    onChange: (templateIDs: string[], templates: Content[]) => void
    compatibleStructures?: string[] // structureIDs
    disabled?: boolean
    label?: string
    variant?: SelectProps['variant']
    required?: boolean
    error?: string
    classifications?: string[]
    allowedTemplateIDs?: string[]
}

export function TemplateFilter({
    value,
    onChange,
    disabled = false,
    label = 'Templates',
    variant = 'outlined',
    required = false,
    error,
    classifications,
    allowedTemplateIDs
}: TemplateFilterProps) {
    const result = useTemplatesQuery({
        page: 1,
        pageSize: 1000,
        TemplateType: 'all',
        Classifications: classifications
    })

    const templates = useMemo(() => {
        if (!result.data) {
            return undefined
        }

        const filteredTemplates = result.data.Rows.filter((t) => {
            if (allowedTemplateIDs?.length) {
                return allowedTemplateIDs?.includes(t.ID)
            }

            return true
        })

        return filteredTemplates
    }, [result?.data?.Rows, allowedTemplateIDs])

    const templateIdToTemplate = useMemo(
        () =>
            templates?.reduce(
                (a, t) => ({
                    ...a,
                    [t.ID]: t
                }),
                {} as Record<string, Content>
            ),
        [templates]
    )

    const filteredValues = useMemo(() => {
        if (!templateIdToTemplate) return []

        return value.filter((templateId) => templateId in templateIdToTemplate)
    }, [value, templateIdToTemplate])

    if (result.isLoading || !templateIdToTemplate) {
        return <CenteredSpinner />
    }

    if (result.isError) {
        return <Alert severity='error'>Error loading templates: {guessErrorMessage(result.error)}</Alert>
    }

    return (
        <FormControl variant={variant || 'outlined'} required={required} sx={{ width: '100%' }} disabled={disabled}>
            <Autocomplete
                disabled={disabled}
                sx={{
                    flex: 1
                }}
                aria-required={required}
                multiple
                disableCloseOnSelect
                disablePortal
                options={templates || []}
                value={filteredValues.map((id) => templateIdToTemplate[id])}
                onChange={(ev, newValue) => {
                    onChange(
                        newValue.map((r) => r.ID),
                        newValue
                    )
                }}
                getOptionLabel={(option) => {
                    return option.Title
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label || 'Type'}
                        variant={variant || 'outlined'}
                        required={required}
                    />
                )}
                renderOption={(props, option, { selected }) => {
                    const { ...optionProps } = props
                    return (
                        <li key={option.ID} {...optionProps}>
                            <Checkbox style={{ marginRight: 8 }} checked={selected} size='small' />
                            <Typography textTransform='capitalize'>{option.Title}</Typography>
                        </li>
                    )
                }}
            />
        </FormControl>
    )
}
