import { useParams } from 'react-router-dom'
import { CustomDrawerItem, useCustomDrawerItemsQuery } from './queries'

export function useURLParamMenuItem() {
    const params = useParams()
    const configID = params['configID']
    const customDrawerItemRoute = !!configID ? `/${configID}` : ''

    const results = useCustomDrawerItemsQuery()
    return {
        ...results,
        data: !!configID ? (results?.data?.find((cdi) => cdi.Route == customDrawerItemRoute) as CustomDrawerItem) : null
    }
}
