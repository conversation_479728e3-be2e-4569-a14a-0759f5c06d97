import { httpDelete, httpGet, httpPost, httpPut } from '@/common/client'
import { CustomDrawerItemAPI } from '@/common/constants'
import { baseQueryConfig } from '@/common/react-query'
import { useIdentity } from '@/pkgs/auth/atoms'
import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'

const customDrawerItemDto = z.object({
    Route: z.string(), // "/exampleRoute"
    Label: z.string(),
    IconName: z.string().nullish(),
    Description: z.string().nullish(),
    ContentType: z.string(),
    Templates: z.array(z.string()).nullish(),
    Structures: z.array(z.string()).nullish(),
    Roles: z.array(z.string()).nullish(),
    Tags: z.array(z.string()).nullish(),
    TagsMinMax: z.array(z.number()),
    Active: z.boolean()
})

const customDrawerItem = customDrawerItemDto.extend({
    ID: z.string()
})

export type CustomDrawerItemDTO = z.infer<typeof customDrawerItemDto>
export type CustomDrawerItem = z.infer<typeof customDrawerItem>

export function useCustomDrawerItemsQuery() {
    return useQuery({
        ...baseQueryConfig,
        queryKey: ['custom-drawer-items'],
        queryFn: async () => httpGet(CustomDrawerItemAPI, null, z.array(customDrawerItem))
    })
}

// returns CDIs only visible to current user
export function useCustomDrawerItemsForUser(): CustomDrawerItem[] {
    const identity = useIdentity()
    const userRoleIDs = identity?.Groups?.map((grp) => grp.Role?.id)

    const { data } = useQuery({
        ...baseQueryConfig,
        queryKey: ['custom-drawer-items'],
        queryFn: async () => httpGet(CustomDrawerItemAPI, null, z.array(customDrawerItem))
    })

    if (identity.IsAdmin) return data || []

    return (
        data?.filter((cdi) => {
            if (!cdi.Roles?.length) return true

            const allowed: boolean = !!cdi?.Roles?.length
                ? !!userRoleIDs.filter((id) => cdi?.Roles!.includes(id)).length
                : true
            return allowed
        }) || []
    )
}

export function createCustomDrawerItem(item: CustomDrawerItemDTO) {
    return httpPost(CustomDrawerItemAPI, item, z.string())
}

export function updateCustomDrawerItem(item: CustomDrawerItem) {
    return httpPut(`${CustomDrawerItemAPI}`, item, z.string())
}

export function updateCustomDrawerItems(items: CustomDrawerItem[]) {
    return httpPut(`${CustomDrawerItemAPI}/all`, items, z.string())
}

export function deleteCustomDrawerItem(id: string) {
    return httpDelete(`${CustomDrawerItemAPI}/${id}`)
}
