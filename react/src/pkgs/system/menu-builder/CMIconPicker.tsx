import React from 'react'
import { Select, MenuItem, FormControl, InputLabel, Box, Typography } from '@mui/material'
import CreateIcon from '@mui/icons-material/Create'
import DeleteIcon from '@mui/icons-material/Delete'
import FileCopyIcon from '@mui/icons-material/FileCopy'
import RssFeedIcon from '@mui/icons-material/RssFeed'
import MailIcon from '@mui/icons-material/Mail'
import DashboardIcon from '@mui/icons-material/Dashboard'
import ViewListIcon from '@mui/icons-material/ViewList'
import NavigationIcon from '@mui/icons-material/Navigation'
import WarningIcon from '@mui/icons-material/Warning'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import TimerOffIcon from '@mui/icons-material/TimerOff'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'

export const iconMap = {
    CreateIcon,
    FileCopyIcon,
    RssFeedIcon,
    MailIcon,
    DashboardIcon,
    WarningIcon,
    DeleteIcon,
    CheckCircleIcon,
    TimerOffIcon,
    MoreHorizIcon,

    ViewListIcon,
    NavigationIcon
}

export type IconName = keyof typeof iconMap

interface CMIconPickerProps {
    label?: string
    value: IconName
    onChange: (iconName: IconName) => void
}

export function CMIconPicker({ label = 'Select Icon', value, onChange }: CMIconPickerProps) {
    return (
        <FormControl fullWidth>
            <InputLabel>{label}</InputLabel>
            <Select
                value={value}
                label={label}
                onChange={(e) => onChange(e.target.value as IconName)}
                renderValue={(selected) => {
                    const SelectedIcon = iconMap[selected as IconName]
                    return (
                        <Box display='flex' alignItems='center' gap={1}>
                            <SelectedIcon />
                            <Typography>{selected}</Typography>
                        </Box>
                    )
                }}
            >
                {Object.entries(iconMap).map(([key, Icon]) => (
                    <MenuItem key={key} value={key}>
                        <Box display='flex' alignItems='center' gap={1}>
                            <Icon />
                            <Typography>{key}</Typography>
                        </Box>
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    )
}
