import { routes, useDrawerItems } from '@/app/routes'
import Page<PERSON><PERSON>r<PERSON>ithHeader from '@/common/components/PageContainerWithHeader'
import RoleFilter from '@/pkgs/user-management/roles/RoleFilter'
import RoleSelect from '@/pkgs/user-management/roles/RoleSelect'
import { useMemo, useState } from 'react'
import { MenuBuilderForm, MenuBuilderFormData, MenuBuilderFormDataErrors } from './MenuBuilderForm'
import { AddButton, ConfirmAction } from '@/common/components'
import {
    createCustomDrawerItem,
    CustomDrawerItem,
    CustomDrawerItemDTO,
    deleteCustomDrawerItem,
    updateCustomDrawerItem,
    updateCustomDrawerItems,
    useCustomDrawerItemsQuery
} from './queries'
import { useMutation } from '@tanstack/react-query'
import { ContentType, ContentTypeToTitle } from '@/pkgs/content/types'
import { Sortables } from '@/pkgs/ordered-lists/Sortables'
import { <PERSON>, Button, DialogActions, Dialog<PERSON>ontent, FormHelperText, Stack, Typography } from '@mui/material'
import CMDialog from '@/common/components/CMDialog'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { _isEqual, notify } from '@/helpers'
import { MenuLightCell } from '@/pkgs/grid/cells/MenuLightCell'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import CreateIcon from '@mui/icons-material/Create'
import DeleteIcon from '@mui/icons-material/Delete'
import { colours } from '@/common/colours'
import CMLink from '@/common/CMLink'
import { getFeaturedMenuItemPath } from '@/app/useAppNavigation'

function getDefaultMenuBuilderFormData(): MenuBuilderFormData {
    return {
        Route: '',
        Label: '',
        ContentType: ContentType.Page,
        TagsMinMax: [0, 999],
        Active: true,
        IconName: 'CreateIcon'
    }
}

export function MenuBuilder() {
    const [menuBuilderFormMode, setMenuBuilderFormMode] = useState<'Create' | 'Edit'>('Create')

    const [formData, setFormData] = useState<MenuBuilderFormData>(getDefaultMenuBuilderFormData())
    const [formDataErrors, setFormDataErrors] = useState<MenuBuilderFormDataErrors>({})
    const [onSubmitError, setOnSubmitError] = useState('')

    const [formDialogIsOpen, setFormDialogIsOpen] = useState(false)
    const [confirmDeleteDialogIsOpen, setConfirmDeleteDialogIsOpen] = useState(false)

    const { data, refetch } = useCustomDrawerItemsQuery()

    const originalFormData = useMemo(() => {
        return data?.find((cdi) => cdi.ID == (formData as CustomDrawerItem)?.ID)
    }, [data, formData])

    const hasChanges = useMemo(() => {
        return !_isEqual(formData, originalFormData)
    }, [formData, originalFormData])

    const createMutation = useMutation({
        mutationFn: (item: CustomDrawerItemDTO) => createCustomDrawerItem(item),
        onSuccess: (data) => {
            notify('Success!', 'success')
            setFormDialogIsOpen(false)
            refetch()
        },
        onError: (err) => {
            const errorMsg = guessErrorMessage(err)
            setOnSubmitError(errorMsg)
        }
    })

    const updateMutation = useMutation({
        mutationFn: (item: CustomDrawerItem) => updateCustomDrawerItem(item),
        onSuccess: (data) => {
            refetch()
            setFormDialogIsOpen(false)
        },
        onError: (err) => {
            const errorMsg = guessErrorMessage(err)
            setOnSubmitError(errorMsg)
        }
    })

    const updateOrderMutation = useMutation({
        mutationFn: (items: CustomDrawerItem[]) => updateCustomDrawerItems(items),
        onSuccess: (data) => {
            notify('Success!', 'success')
            refetch()
            setFormDialogIsOpen(false)
        },
        onError: (err) => {
            const errorMsg = guessErrorMessage(err)
            setOnSubmitError(errorMsg)
        }
    })

    const deleteMutation = useMutation({
        mutationFn: (id: string) => deleteCustomDrawerItem(id),
        onSuccess: () => {
            notify(`Success! Deleted ${formData.Label}`, 'success')
            refetch()
            setConfirmDeleteDialogIsOpen(false)
        }
    })

    const menuItems = (item: CustomDrawerItem) => {
        return (onClose: () => void) => {
            return [
                <CustomMenuItem
                    key={'0'}
                    text={'Edit Item'}
                    onClick={() => {
                        setMenuBuilderFormMode('Edit')
                        setFormData(item)
                        setFormDialogIsOpen(true)
                        onClose()
                    }}
                >
                    <CreateIcon />
                </CustomMenuItem>,
                <CustomMenuItem
                    key={'1'}
                    text={'Delete Item'}
                    onClick={() => {
                        setFormData(item)
                        setConfirmDeleteDialogIsOpen(true)
                        onClose()
                    }}
                >
                    <DeleteIcon />
                </CustomMenuItem>
            ]
        }
    }

    return (
        <PageContainerWithHeader
            title='Menu Builder'
            topRightElement={
                <AddButton
                    title={`add menu item`}
                    func={() => {
                        setMenuBuilderFormMode('Create')
                        setFormData(getDefaultMenuBuilderFormData())
                        setFormDialogIsOpen(true)
                    }}
                />
            }
        >
            {data && (
                <Sortables
                    key={JSON.stringify(data)}
                    value={data || []}
                    onChange={(items) => {
                        updateOrderMutation.mutate(items)
                    }}
                    renderValue={(item) => {
                        return (
                            <Stack
                                key={JSON.stringify(item)}
                                direction='row'
                                sx={{ justifyContent: 'space-between', alignItems: 'center' }}
                            >
                                <Stack>
                                    <Stack direction='row' gap='0.8rem' sx={{ alignItems: 'center' }}>
                                        {!item.Active && (
                                            <Typography variant='subtitle1' sx={{ color: colours.expired }}>
                                                (Inactive)
                                            </Typography>
                                        )}
                                        {!item.Active ? (
                                            <Typography variant='h6'>{item.Label}</Typography>
                                        ) : (
                                            <CMLink to={getFeaturedMenuItemPath(item.Route)}>
                                                <Typography variant='h6'>{item.Label}</Typography>
                                            </CMLink>
                                        )}
                                    </Stack>

                                    {item.Description && <Typography>{item.Description}</Typography>}
                                    <Typography>
                                        Route: <strong>{item.Route}</strong>
                                    </Typography>
                                    <Typography>
                                        Content Type: <strong>{ContentTypeToTitle[item.ContentType]}</strong>
                                    </Typography>
                                </Stack>
                                <MenuLightCell itemsFactory={menuItems(item)} />
                            </Stack>
                        )
                    }}
                />
            )}
            <ConfirmAction
                title='Are you sure?'
                text={`Deleting ${formData.Label}`}
                open={confirmDeleteDialogIsOpen}
                handleAgree={() => {
                    if ('ID' in formData) {
                        deleteMutation.mutate((formData as CustomDrawerItem).ID)
                        setConfirmDeleteDialogIsOpen(false)
                    }
                }}
                handleClose={() => {
                    setConfirmDeleteDialogIsOpen(false)
                }}
                handleDisagree={() => {
                    setConfirmDeleteDialogIsOpen(false)
                }}
            />
            <CMDialog
                open={formDialogIsOpen}
                showCloseButton
                onClose={() => {
                    setFormDialogIsOpen(false)
                }}
                title={`${menuBuilderFormMode} Featured Menu Item`}
                maxWidth='lg'
            >
                <DialogContent sx={{ padding: 0 }}>
                    <MenuBuilderForm
                        formData={formData}
                        formDataErrors={formDataErrors}
                        onChange={(menuItem) => {
                            if (!!onSubmitError) {
                                setOnSubmitError('')
                            }

                            setFormData(menuItem)
                        }}
                        isEditMode={menuBuilderFormMode == 'Edit'}
                    />
                </DialogContent>
                <DialogActions>
                    <Stack direction='column' justifyContent='flex-end'>
                        <Stack direction='row' sx={{ gap: '8px', justifyContent: 'flex-end' }}>
                            <Button
                                disabled={!hasChanges}
                                variant='text'
                                color='primary'
                                type='reset'
                                onClick={() => {
                                    if (menuBuilderFormMode == 'Edit') {
                                        if (originalFormData) {
                                            setFormData(originalFormData)
                                        }

                                        return
                                    }

                                    setFormData(getDefaultMenuBuilderFormData())
                                }}
                            >
                                Reset Changes
                            </Button>
                            <Button
                                disabled={!hasChanges}
                                variant='contained'
                                color='primary'
                                type='submit'
                                onClick={() => {
                                    if (menuBuilderFormMode == 'Create') {
                                        createMutation.mutate(formData)
                                    } else if (menuBuilderFormMode == 'Edit') {
                                        updateMutation.mutate(formData as CustomDrawerItem)
                                    } else {
                                        //
                                    }
                                }}
                            >
                                Save
                            </Button>
                        </Stack>
                        {onSubmitError && (
                            <FormHelperText error={true}>
                                <Typography>{onSubmitError}</Typography>
                            </FormHelperText>
                        )}
                    </Stack>
                </DialogActions>
            </CMDialog>
        </PageContainerWithHeader>
    )
}
