import {
    Box,
    Button,
    Chip,
    <PERSON>alogActions,
    DialogContent,
    FormControl,
    FormHelperText,
    Grid,
    InputAdornment,
    Typography
} from '@mui/material'
import { useCallback, useContext, useEffect, useRef, useState } from 'react'
import CMDialog from '@/common/components/CMDialog'
import { TagsSelector } from '../system/tags/TagsSelector'
import { TagType } from '../system/tags/types'
import { EntityScopeEnum } from '../auth/entityScope'
import { PrivacyLevel } from '../content/PrivacyLevel'
import { useAppContext, useCurrentSiteID } from '../auth/atoms'
import { BoxForm } from '@/common/components/BoxForm'
import { colours } from '@/common/colours'
import { useMutation, useQuery } from '@tanstack/react-query'
import { z } from 'zod'
import { notify } from '@/helpers'
import { LoadingButton } from '@mui/lab'
import CMTextField from '@/common/components/CMTextField'
import { dateTime } from '@/common/react-query'
import { documentService } from '../media/document/document.service'
import DocumentLinkButton from '../media/document/documents/DocumentLinkButton'
import { useDropzone } from 'react-dropzone'
import _ from 'lodash'
import { documentContext } from '../media/document/context'
import { ResourceLocationButton } from '@/pkgs/storage/ResourceLocationButton'
import { DocumentViewer } from '@/pkgs/storage/DocumentViewer'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { SitesSelectorComponent } from '@/common/components/selectors/SitesSelectorComponent'
import { DocumentFilenameTextField } from './DocumentFilenameTextField'
import { filenameRegex, titleRegex } from '@/pkgs/media/upload/documentFilenameToTitle'

function useDocumentUploadDropzone(replaceFn, fileType) {
    const onDrop = useCallback((acceptedFiles) => {
        acceptedFiles.forEach((file) => {
            const reader = new FileReader()
            reader.readAsDataURL(file)
            reader.onabort = () => console.log('file reading was aborted')
            reader.onerror = () => console.log('file reading has failed')
            reader.onloadend = () => {
                console.log('reader.result', reader.result)
                replaceFn?.(reader.result)
            }
        })
    }, [])

    const config = {
        onDrop,
        accept: [
            '.pdf',
            '.docx',
            '.doc',
            '.pptx',
            '.xlsx',
            '.xls',
            '.pub',
            '.ppt',
            '.kmz',
            '.kml',
            '.zip',
            '.woff2',
            '.ttf'
        ]
    }
    if (fileType) {
        config['accept'] = [`.${fileType}`]
    }

    return useDropzone(config)
}

export const document = z.object({
    id: z.string(),
    type: z.string(), // document | folder
    department_id: z.string().nullish(),
    sites: z.array(z.string()).nonempty('Please select at least one site'),
    filename: z
        .string()
        .regex(
            filenameRegex,
            'Filename can only contain letters, numbers, dashes, underscores and be less than 220 characters long'
        ),
    title: z
        .string()
        .regex(
            titleRegex,
            "Title must start with a letter or number, be 3-150 characters long, and may only contain letters, numbers, spaces, and common punctuation (', ., -, &, #, parentheses)."
        ),
    tags: z.array(z.string()).nullish(),
    privacyLevel: z.number(),
    created: dateTime,
    updated: dateTime,
    deleted: dateTime,
    active: z.boolean()
})

type Document = z.infer<typeof document>

interface DocumentEditorProps {
    documentId: string
    isOpen: boolean
    onClose: () => void
    onDelete: () => void
}

export default function DocumentEditor({ documentId, isOpen, onClose, onDelete }: DocumentEditorProps) {
    const [documents, setDocuments] = useContext(documentContext)
    const currentSiteID = useCurrentSiteID()
    const documentUploadRef = useRef<any>(null)
    const appContext = useAppContext()
    const {
        data: currentDocument,
        isLoading: currentDocumentIsLoading,
        refetch,
        isError: currentDocumentIsError
    } = useQuery({
        queryKey: ['getDocument', documentId],
        queryFn: async () => documentService.getDocumentById(currentSiteID!, documentId)
    })

    const [formData, setFormData] = useState(currentDocument)
    const [formDataErrors, setFormDataErrors] = useState<Partial<Document>>()

    useEffect(() => {
        setFormData(currentDocument)
    }, [currentDocument])

    const updateDocumentMutation = useMutation({
        mutationFn: (document: Document) => documentService.editDocument(currentSiteID!, document)
    })

    const replaceDocumentMutation = useMutation({
        mutationFn: (base64: string) => documentService.replaceDocument(currentSiteID!, formData.id, base64)
    })

    const deleteDocumentMutation = useMutation({
        mutationFn: (documentId: string) => documentService.deleteDocuments(currentSiteID!, [documentId])
    })

    const isLoading =
        currentDocumentIsLoading ||
        updateDocumentMutation.isLoading ||
        replaceDocumentMutation.isLoading ||
        deleteDocumentMutation.isLoading

    const [hasError, setHasError] = useState(false)

    useEffect(() => {
        setHasError(false)
    }, [formData])

    // replace document
    const [newDocument, setNewDocument] = useState<string | null>(null)

    const extension = formData?.filename.split('.').pop()

    const { getInputProps } = useDocumentUploadDropzone((doc) => setNewDocument(doc), extension)

    const updateDocument = async () => {
        if (!formData) return
        if (!formData?.title) {
            setFormDataErrors((prev) => ({ ...prev, ['title']: 'title cannot be empty.' }))
            return
        }

        if (!formData?.filename) {
            setFormDataErrors((prev) => ({ ...prev, ['filename']: 'filename cannot be empty.' }))
            return
        }

        const parsed = document.safeParse(formData)
        if (!parsed.success) {
            Object.keys(parsed.error.flatten().fieldErrors).forEach((key) => {
                setFormDataErrors((prev) => ({ ...prev, [key]: parsed.error.flatten().fieldErrors[key][0] }))
            })
            return
        }

        const data = _.cloneDeep(formData)

        updateDocumentMutation.mutate(data, {
            onSuccess: (res) => {
                notify(`Document ${currentDocument.filename} updated`, 'info')
                refetch()
                const clone = _.cloneDeep(documents)
                const elIndex = clone.findIndex((x) => x.id === res.id)
                if (elIndex > -1) {
                    clone[elIndex] = res
                    // @ts-ignore
                    setDocuments(clone)
                }
            },
            onError: (err) => {
                console.error('update document error', err)
                const msg = guessErrorMessage(err)
                if (msg.includes('title') || msg.includes('document')) {
                    setFormDataErrors({
                        ...formDataErrors,
                        [msg?.includes('title') ? 'title' : 'filename']: guessErrorMessage(err)
                    })
                }

                setHasError(true)
                notify(err, 'error')
            }
        })
    }

    const replaceDocument = async () => {
        if (!newDocument) return

        replaceDocumentMutation.mutate(newDocument, {
            onSuccess: (res) => {
                notify(`Document ${currentDocument.filename} succesfully replaced`, 'info')
                refetch()
            },
            onError: (err) => {
                console.error('replace document error', err)
                notify(err, 'error')
                setHasError(true)
            }
        })
    }

    const deleteDocument = async () => {
        if (!formData?.id) return

        deleteDocumentMutation.mutate(formData.id, {
            onSuccess: (res) => {
                notify(`Document ${formData.filename} deleted`, 'info')
                onDelete()
            },
            onError: (err) => {
                console.error('update document error', err)
                notify(err, 'error')
                setHasError(true)
            }
        })
    }

    const EditorTitle = useCallback(() => {
        return (
            <>
                Document Editor: {currentDocument?.filename}
                <Typography fontStyle='italic'>
                    Adjust attributes such as filename. Delete or replace document and change where it is shared.
                </Typography>
            </>
        )
    }, [formData])

    const EditorActions = useCallback(() => {
        return (
            <DialogActions
                sx={{
                    justifyContent: 'space-between',
                    borderTop: `1px solid ${colours.off_white_but_darker}`,
                    padding: '16px 24px'
                }}
            >
                {currentDocument && (
                    <>
                        <BoxForm boxRowGap='sm'>
                            <Box className='box-row'>
                                <Button
                                    disabled={isLoading}
                                    variant='outlined'
                                    role={undefined}
                                    component='label'
                                    tabIndex={-1}
                                    // onClick={() => setIsReplacing(true)}
                                >
                                    Replace Document
                                    <input {...getInputProps()} />
                                </Button>
                                <DocumentLinkButton
                                    document={{
                                        id: currentDocument.id,
                                        filename: currentDocument.filename,
                                        type: 'document',
                                        sites: currentDocument.sites,
                                        department_id: currentDocument.department_id
                                    }}
                                />
                                <Button
                                    disabled={isLoading || !appContext.action(formData, 'delete')}
                                    style={{ marginRight: 'auto' }}
                                    variant='outlined'
                                    color='error'
                                    onClick={() => {
                                        if (window.confirm('Are you sure you want to delete this document?')) {
                                            deleteDocument()
                                        }
                                    }}
                                >
                                    Delete Document
                                </Button>
                            </Box>
                        </BoxForm>
                        <BoxForm>
                            <Box className='box-row'>
                                {hasError ||
                                    (currentDocumentIsError && (
                                        <FormHelperText error={true}>
                                            Something went wrong. Please try again.
                                        </FormHelperText>
                                    ))}
                                {newDocument && <Button onClick={() => setNewDocument(null)}>Cancel</Button>}
                                <LoadingButton
                                    loading={isLoading}
                                    disabled={
                                        isLoading ||
                                        Object.keys(formDataErrors || {}).some((key) => !!formDataErrors?.[key]) ||
                                        !appContext.action(formData, 'update')
                                    }
                                    variant='contained'
                                    onClick={() => {
                                        if (newDocument) {
                                            replaceDocument()
                                        } else {
                                            updateDocument()
                                        }
                                    }}
                                    color={newDocument ? 'warning' : undefined}
                                >
                                    {newDocument ? 'Replace' : 'Save'}
                                </LoadingButton>
                            </Box>
                        </BoxForm>
                    </>
                )}
            </DialogActions>
        )
    }, [appContext, formData, documentUploadRef, isLoading])

    return (
        <CMDialog fullWidth maxWidth='xl' open={isOpen} title={<EditorTitle />} showCloseButton onClose={onClose}>
            <DialogContent sx={{ overflowY: 'hidden' }}>
                <Grid container sx={{ height: '60vh', maxHeight: '60vh', overflowY: 'hidden' }}>
                    <Grid item md={7.5} xs={12} sx={{ paddingX: '24px', height: '100%', overflowY: 'hidden' }}>
                        <DocumentViewer
                            extension={formData?.filename.split('.').pop()}
                            documentId={formData?.id}
                            replacementBinary={newDocument}
                            loading={currentDocumentIsLoading}
                            error={
                                !(
                                    formData?.filename.endsWith('pdf') ||
                                    formData?.filename.endsWith('kml') ||
                                    formData?.filename.endsWith('kmz')
                                )
                            }
                        />
                    </Grid>
                    <Grid item md={4.5} xs={12} sx={{ paddingX: '24px', overflowY: 'auto', height: '100%' }}>
                        {formData && (
                            <BoxForm>
                                <CMTextField
                                    required
                                    error={!!formDataErrors?.title}
                                    errorText={formDataErrors?.title}
                                    margin={'dense'}
                                    label='Title'
                                    value={formData?.title}
                                    onChange={(e) => {
                                        setFormData((prev) => ({ ...prev, title: e.target.value }))
                                        if (!!formDataErrors?.title) {
                                            setFormDataErrors((prev) => ({ ...prev, title: '' }))
                                        }
                                    }}
                                    fullWidth
                                />

                                <DocumentFilenameTextField
                                    // required
                                    error={!!formDataErrors?.filename}
                                    errorText={formDataErrors?.filename}
                                    margin={'dense'}
                                    label='Filename'
                                    value={formData?.filename}
                                    onChange={(v) => {
                                        setFormData((prev) => prev && { ...prev, filename: v })

                                        if (!!formDataErrors?.filename) {
                                            setFormDataErrors((prev) => ({ ...prev, filename: '' }))
                                        }
                                    }}
                                    fullWidth
                                />

                                <FormControl fullWidth>
                                    <TagsSelector
                                        selected={formData?.tags || []}
                                        disabled={false}
                                        tagTypes={[TagType.Document]}
                                        onChange={(tags) => setFormData((prev) => prev && { ...prev, tags: tags })}
                                    />
                                </FormControl>
                                <FormControl fullWidth>
                                    <SitesSelectorComponent
                                        value={{
                                            Sites: formData?.sites,
                                            DepartmentID: formData?.department_id
                                        }}
                                        onChange={(v) => {
                                            setFormData(
                                                (prev) =>
                                                    prev && {
                                                        ...prev,
                                                        sites: v.Sites,
                                                        department_id: v.DepartmentID
                                                    }
                                            )
                                        }}
                                        contentType={EntityScopeEnum.Image}
                                        error={formDataErrors?.sites?.join(', ')}
                                    />
                                </FormControl>
                                <FormControl fullWidth>
                                    <PrivacyLevel
                                        value={formData?.privacyLevel}
                                        onChange={(v) => setFormData((p) => ({ ...p, privacyLevel: v }))}
                                        isDisabled={false}
                                    />
                                </FormControl>
                                {currentDocument && (
                                    <ResourceLocationButton
                                        resources={[
                                            `${currentDocument.type}/${currentDocument.id}`,
                                            `${currentDocument.type}/${currentDocument.filename}`,
                                            `${currentDocument.type}/${encodeURIComponent(currentDocument.filename)}`
                                        ]}
                                    />
                                )}
                                <Box>
                                    <FormHelperText>
                                        Last updated: {new Date(currentDocument?.updated).toLocaleString()}
                                    </FormHelperText>
                                    <FormHelperText>Id: {currentDocument?.id}</FormHelperText>
                                </Box>
                            </BoxForm>
                        )}
                    </Grid>
                </Grid>
            </DialogContent>
            <EditorActions />
        </CMDialog>
    )
}
