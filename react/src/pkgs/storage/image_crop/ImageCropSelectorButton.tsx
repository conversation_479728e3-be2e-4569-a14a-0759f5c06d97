import CustomIconButton from '@/common/components/CustomIconButton'
import { alpha, Box, DialogContent, Stack, Typography } from '@mui/material'
import PictureInPictureIcon from '@mui/icons-material/PictureInPicture'
import { Image, useImageQuery, useImageSearchQuery } from '../queries'
import { colours } from '@/common/colours'
import CMDialog from '@/common/components/CMDialog'
import { forwardRef, useImperativeHandle, useState } from 'react'
import { ImageCropSizeText } from '@/pkgs/system/image-crop-size/ImageCropSizeText'
import { useImageCropSizeQuery } from '@/pkgs/system/image-crop-size/queries'
import { getCropId, getCropPath } from './cropPathUtils'

export interface ImageCropSelectorButtonHandlers {
    openCropList: () => void
}
export interface ImageCropSelectorButtonProps {
    image: Image
    onSelect?: (crop: Image) => void
    allowedCropSizes?: string[]
}

export const ImageCropSelectorButton = forwardRef<ImageCropSelectorButtonHandlers, ImageCropSelectorButtonProps>(
    ({ image, onSelect, allowedCropSizes }: ImageCropSelectorButtonProps, ref) => {
        const allowedCropSizesNormalized = allowedCropSizes?.map((s) => s.toLocaleLowerCase())
        const { data: cropSizes } = useImageCropSizeQuery()
        const baseColour = image?.dirty_image_crop_size_ids?.length ? colours.warning : colours.base_blue
        const existingCropSizes =
            image?.image_crop_size_ids
                ?.map((id) => cropSizes?.Rows?.find((cropSize) => cropSize.ID == id))
                .filter((s) => !!s) || []

        const filteredCropSizes = !allowedCropSizesNormalized?.length
            ? existingCropSizes
            : existingCropSizes?.filter((cropSize) => allowedCropSizesNormalized.includes(cropSize.Name))

        const [cropListIsOpen, setCropListIsOpen] = useState(false)

        useImperativeHandle(ref, () => {
            return {
                openCropList: () => setCropListIsOpen(true)
            }
        })

        if (!existingCropSizes?.length) return undefined

        return (
            <>
                <CustomIconButton
                    onClick={(ev) => {
                        setCropListIsOpen(true)
                    }}
                    sx={{
                        color: colours.white,
                        backgroundColor: baseColour
                    }}
                    shape='PILL'
                >
                    <Typography>{filteredCropSizes.length}</Typography>
                    <PictureInPictureIcon />
                </CustomIconButton>
                <CMDialog
                    fullWidth
                    open={cropListIsOpen}
                    showCloseButton
                    onClose={() => setCropListIsOpen(false)}
                    title={`${image.filename}`}
                >
                    <DialogContent>
                        <Stack direction='column' gap='8px' sx={{ height: '100%', marginY: '24px' }}>
                            {filteredCropSizes?.length == 0 && (
                                <Typography fontStyle='italic'>
                                    Crops exist for this image but are not available for selection.
                                </Typography>
                            )}
                            {filteredCropSizes?.map((crop, index) => {
                                const cropToBeReplaced = image?.dirty_image_crop_size_ids?.includes(crop.ID)
                                return (
                                    <Box
                                        key={crop.ID}
                                        onClick={() => {
                                            const cropAsImage: Image = {
                                                ...image,
                                                id: getCropId(image.id, crop.Name)
                                            }
                                            onSelect?.(cropAsImage)
                                        }}
                                        sx={{
                                            position: 'relative',
                                            boxShadow: `0 0 3px ${cropToBeReplaced ? baseColour : colours.base_blue}`,
                                            border: `2px solid ${cropToBeReplaced ? baseColour : colours.base_blue}`,
                                            borderRadius: '8px',
                                            '&:hover': {
                                                backgroundColor: alpha(
                                                    cropToBeReplaced ? baseColour : colours.base_blue,
                                                    0.5
                                                )
                                            },
                                            height: '100px',
                                            padding: '8px',

                                            cursor: onSelect ? 'pointer' : undefined,
                                            display: 'flex',
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <img
                                            crossOrigin='anonymous'
                                            src={getCropPath(image.id, crop.Name, image.updated)}
                                            style={{
                                                border: '1px solid black',
                                                objectFit: 'contain',
                                                height: 'auto',
                                                borderRadius: '8px',
                                                maxHeight: '95%',
                                                maxWidth: '100%'
                                            }}
                                        />
                                        <ImageCropSizeText sizeId={crop.ID || ''} />
                                    </Box>
                                )
                            })}
                        </Stack>
                    </DialogContent>
                </CMDialog>
            </>
        )
    }
)
