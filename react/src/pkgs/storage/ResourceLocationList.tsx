import { <PERSON><PERSON>, <PERSON>, Button, Typography } from '@mui/material'
import { ResourceRef } from './queries'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { useBatchedContentDetails } from '@/pkgs/content/queries'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useAppNavigation } from '@/app/useAppNavigation'
import CMLink from '@/common/CMLink'
import Link from '@mui/material/Link'
import { Content } from '@/pkgs/content/types'
import { FragmentEditor } from '@/pkgs/content/fragments/FragmentEditor'
import React, { useState } from 'react'
import { useListDetailsQuery } from '@/pkgs/ordered-lists/queries'
import { ListForm } from '@/pkgs/ordered-lists/ListForm'
import { useStructureByIdQuery } from '@/pkgs/structure/queries'

export function ResourceLocationList({ refs }: { refs: ResourceRef[] | undefined | null }) {
    if (!refs?.length) {
        return (
            <Box>
                <Typography variant='subtitle1' textAlign='center' sx={{ mt: 1 }}>
                    This resource is not used in any Pages, News, Events, Fragments or List.
                </Typography>
            </Box>
        )
    }

    return (
        <Box sx={{ m: 2 }}>
            {refs &&
                refs.length > 0 &&
                refs.map((resource) => {
                    switch (resource.EntityType) {
                        case 'alert':
                        case 'news':
                        case 'event':
                        case 'page':
                        case 'fragment':
                        case 'distributed_page':
                        case 'template':
                            return <ContentRef key={resource.EntityID} reference={resource} />
                        case 'list':
                            return <ListRef key={resource.EntityID} reference={resource} />
                        case 'structure':
                            return <StructureRef key={resource.EntityID} reference={resource} />
                        default:
                            return (
                                <Box key={resource.EntityID} sx={{ mt: 2 }}>
                                    <Typography variant='subtitle1'>
                                        {resource.EntityType}/{resource.EntityID}
                                    </Typography>
                                </Box>
                            )
                    }
                })}
        </Box>
    )
}

function ListRef({ reference }: { reference: ResourceRef }) {
    const query = useListDetailsQuery(reference.EntityID)
    const [listEditorIsOpen, setListEditorIsOpen] = useState(false)

    if (query.isLoading) return <CenteredSpinner key={reference.EntityID} />
    if (query.error)
        return (
            <Alert key={reference.EntityID} severity='error'>
                {guessErrorMessage(query.error)}
            </Alert>
        )
    if (!query.data)
        return (
            <Alert key={reference.EntityID} severity='warning'>
                List not found: {reference.EntityType}/{reference.EntityID}. It may have been deleted.
            </Alert>
        )

    const list = query.data

    return (
        <>
            <Box key={list.ID} sx={{ mt: 2 }}>
                <Typography variant='subtitle1'>{list.Name || 'Untitled'}</Typography>
                <Button onClick={() => setListEditorIsOpen(true)} sx={{}}>
                    <Typography variant='caption' color='text.secondary'>
                        {'list'}/{list.ID}
                    </Typography>
                </Button>
            </Box>

            {listEditorIsOpen && (
                <ListForm
                    // @ts-ignore
                    value={list}
                    open={listEditorIsOpen}
                    onClose={() => {
                        setListEditorIsOpen(false)
                    }}
                    onSave={() => {
                        query.refetch()
                    }}
                    editAs={'individual'}
                />
            )}
        </>
    )
}

function ContentRef({ reference }: { reference: ResourceRef }) {
    const contentResult = useBatchedContentDetails(reference.EntityID)
    const { getPath } = useAppNavigation()

    if (contentResult.isLoading) return <CenteredSpinner key={reference.EntityID} />
    if (contentResult.error)
        return (
            <Alert key={reference.EntityID} severity='error'>
                {guessErrorMessage(contentResult.error)}
            </Alert>
        )
    if (!contentResult.data)
        return (
            <Alert key={reference.EntityID} severity='warning'>
                Content not found: {reference.EntityType}/{reference.EntityID}. It may have been deleted.
            </Alert>
        )

    if (contentResult.data?.Type == 'distributed_page') {
        return <DistributedPageRef page={contentResult.data} />
    }

    if (contentResult.data?.Type == 'fragment') {
        return <FragmentRef fragment={contentResult.data} />
    }

    const path =
        contentResult.data?.Type == 'template'
            ? getPath(`/template-editor/${reference.EntityID}`)
            : getPath(`/content-editor/${reference.EntityID}`)

    return (
        <Box key={reference.EntityID} sx={{ mt: 2 }}>
            <Typography variant='subtitle1'>{contentResult.data?.Title || 'Untitled'}</Typography>
            <Link href={`${path}`} target='_blank' rel='noopener noreferrer'>
                <Typography variant='caption' color='text.secondary'>
                    {reference.EntityType}/{reference.EntityID}
                </Typography>
            </Link>
        </Box>
    )
}

function StructureRef({ reference }: { reference: ResourceRef }) {
    const result = useStructureByIdQuery(reference.EntityID)

    const { getPath } = useAppNavigation()

    if (result.isLoading) return <CenteredSpinner key={reference.EntityID} />
    if (result.error)
        return (
            <Alert key={reference.EntityID} severity='error'>
                {guessErrorMessage(result.error)}
            </Alert>
        )
    if (!result.data)
        return (
            <Alert key={reference.EntityID} severity='warning'>
                Content not found: {reference.EntityType}/{reference.EntityID}. It may have been deleted.
            </Alert>
        )

    const path = getPath(`/structure/${reference.EntityID}`)

    return (
        <Box key={reference.EntityID} sx={{ mt: 2 }}>
            <Typography variant='subtitle1'>{result.data?.Name || 'Untitled'}</Typography>
            <Link href={`${path}`} target='_blank' rel='noopener noreferrer'>
                <Typography variant='caption' color='text.secondary'>
                    {reference.EntityType}/{reference.EntityID}
                </Typography>
            </Link>
        </Box>
    )
}

function FragmentRef({ fragment }: { fragment: Content }) {
    const [fragmentEditorIsOpen, setFragmentEditorIsOpen] = useState(false)

    return (
        <>
            <Box key={fragment.ID} sx={{ mt: 2 }}>
                <Typography variant='subtitle1'>{fragment.Title || 'Untitled'}</Typography>
                <Button onClick={() => setFragmentEditorIsOpen(true)} sx={{}}>
                    <Typography variant='caption' color='text.secondary'>
                        {'fragment'}/{fragment.ID}
                    </Typography>
                </Button>
            </Box>
            {fragmentEditorIsOpen && (
                <FragmentEditor
                    dialogMaxWidth='md'
                    id={fragment.ID}
                    open={fragmentEditorIsOpen}
                    onClose={() => {
                        setFragmentEditorIsOpen(false)
                    }}
                    warning={true}
                />
            )}
        </>
    )
}

function DistributedPageRef({ page }: { page: Content }) {
    const refID = page.Path.replace(`${page.ID.replace(/-/g, '')}.`, '')
        .split('.')
        .pop()
    const contentResult = useBatchedContentDetails(formatAsUUID(refID || ''))

    const { getPath } = useAppNavigation()

    if (contentResult.isLoading) return <CenteredSpinner key={refID} />
    if (contentResult.error)
        return (
            <Alert key={refID} severity='error'>
                {guessErrorMessage(contentResult.error)}
            </Alert>
        )
    if (!contentResult.data)
        return (
            <Alert key={refID} severity='warning'>
                Content not found: {page.Type}/{refID}. It may have been deleted.
            </Alert>
        )

    const path = getPath(`/content-editor/${contentResult.data.ID}?siteId=${page.Sites[0]}`)

    return (
        <Box key={refID} sx={{ mt: 2 }}>
            <Typography variant='subtitle1'>{contentResult.data?.Title || 'Untitled'}</Typography>
            <Link href={`${path}`} target='_blank' rel='noopener noreferrer'>
                <Typography variant='caption' color='text.secondary'>
                    {page.Type}/{page.ID} → {contentResult.data.Type}/{contentResult.data.ID}
                </Typography>
            </Link>
        </Box>
    )
}

// required for distributed pages to match with the content ID
function formatAsUUID(input: string): string {
    // Ensure the input is exactly 32 characters long
    if (input.length !== 32) {
        throw new Error('Input string must be exactly 32 characters long.')
    }

    // Insert hyphens at the correct positions
    return `${input.slice(0, 8)}-${input.slice(8, 12)}-${input.slice(12, 16)}-${input.slice(16, 20)}-${input.slice(20)}`
}
