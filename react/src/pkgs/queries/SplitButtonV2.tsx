import { Button, ButtonGroup, ClickAwayListener, Grow, Paper, Popper } from '@mui/material'
import { AddCircleOutline } from '@mui/icons-material'
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'

type SplitButtonBaseProps = {
    variant?: 'text' | 'outlined' | 'contained'
    size?: 'small' | 'medium' | 'large'
    children: React.ReactElement
}

type LabelVariantProps = SplitButtonBaseProps & {
    label: string
    icon: React.JSX.Element
    button?: never
}

type ButtonVariantProps = SplitButtonBaseProps & {
    label?: never
    icon?: never
    button: React.ReactElement
}

type SplitButtonV2Props = LabelVariantProps | ButtonVariantProps

export interface SplitButtonV2Handle {
    close: () => void
}

export const SplitButtonV2 = forwardRef<SplitButtonV2Handle, SplitButtonV2Props>((props, ref) => {
    const { children, variant = 'outlined', size = 'medium' } = props
    const anchorRef = useRef<HTMLDivElement | null>(null)
    const [open, setOpen] = useState(false)
    const handleToggle = () => setOpen((prevOpen) => !prevOpen)

    useImperativeHandle(ref, () => ({
        close: () => setOpen(false)
    }))

    const buttonText = 'label' in props ? props.label : ''
    const icon = 'icon' in props ? props.icon : <AddCircleOutline />

    const mainButton =
        'button' in props ? (
            props.button
        ) : (
            <Button onClick={handleToggle} startIcon={icon} size={size}>
                {buttonText}
            </Button>
        )

    return (
        <>
            <ButtonGroup variant={variant} ref={anchorRef} aria-label='Button group with a nested menu' size={size}>
                {mainButton}
                {'button' in props && (
                    <Button
                        color='inherit'
                        variant={variant}
                        size={size}
                        aria-controls={open ? 'split-button-menu' : undefined}
                        aria-expanded={open ? 'true' : undefined}
                        aria-label='select merge strategy'
                        aria-haspopup='menu'
                        onClick={() => {
                            setOpen((p) => !p)
                        }}
                    >
                        <ArrowDropDownIcon />
                    </Button>
                )}
            </ButtonGroup>
            {/*@ts-ignore*/}
            <Popper
                sx={{ zIndex: 3000 }}
                open={open}
                anchorEl={anchorRef.current}
                role={undefined}
                transition
                disablePortal
                placement={'bottom-start'}
            >
                {({ TransitionProps, placement }) => (
                    <Grow
                        {...TransitionProps}
                        style={{
                            transformOrigin: placement === 'bottom' ? 'center top' : 'center bottom'
                        }}
                    >
                        <Paper>
                            <ClickAwayListener onClickAway={() => setOpen(false)}>{children}</ClickAwayListener>
                        </Paper>
                    </Grow>
                )}
            </Popper>
        </>
    )
})
